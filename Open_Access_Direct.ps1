# فتح Microsoft Access مباشرة
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    فتح Microsoft Access" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

try {
    Write-Host "فتح Microsoft Access..." -ForegroundColor Yellow
    
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    
    Write-Host "✓ تم فتح Microsoft Access بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "تعليمات إنشاء نظام رافع يدوياً:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. في Access، اختر 'Blank Database'" -ForegroundColor White
    Write-Host "2. اسم قاعدة البيانات: RafeaSystem" -ForegroundColor White
    Write-Host "3. اضغط Create" -ForegroundColor White
    Write-Host ""
    Write-Host "4. إنشاء جدول المستخدمين:" -ForegroundColor Cyan
    Write-Host "   - اذهب إلى Create > Table Design" -ForegroundColor White
    Write-Host "   - أضف الحقول التالية:" -ForegroundColor White
    Write-Host "     * UserID (AutoNumber, Primary Key)" -ForegroundColor Gray
    Write-Host "     * Username (Short Text, 50)" -ForegroundColor Gray
    Write-Host "     * Password (Short Text, 50)" -ForegroundColor Gray
    Write-Host "     * FullName (Short Text, 100)" -ForegroundColor Gray
    Write-Host "     * UserType (Short Text, 20)" -ForegroundColor Gray
    Write-Host "   - احفظ الجدول باسم: Users" -ForegroundColor White
    Write-Host ""
    Write-Host "5. إضافة بيانات تجريبية:" -ForegroundColor Cyan
    Write-Host "   - افتح جدول Users" -ForegroundColor White
    Write-Host "   - أضف السجل التالي:" -ForegroundColor White
    Write-Host "     * Username: admin" -ForegroundColor Gray
    Write-Host "     * Password: admin123" -ForegroundColor Gray
    Write-Host "     * FullName: مدير النظام" -ForegroundColor Gray
    Write-Host "     * UserType: مدير" -ForegroundColor Gray
    Write-Host ""
    Write-Host "6. إنشاء وحدة VBA:" -ForegroundColor Cyan
    Write-Host "   - اضغط Alt+F11 لفتح VBA Editor" -ForegroundColor White
    Write-Host "   - اذهب إلى Insert > Module" -ForegroundColor White
    Write-Host "   - انسخ الكود التالي:" -ForegroundColor White
    Write-Host ""
    
    $vbaCode = @'
Public Sub WelcomeToRafea()
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & _
           "تم إنشاء النظام بنجاح!" & vbCrLf & vbCrLf & _
           "معلومات تسجيل الدخول:" & vbCrLf & _
           "اسم المستخدم: admin" & vbCrLf & _
           "كلمة المرور: admin123", vbInformation, "نظام رافع"
End Sub

Public Sub TestSystem()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Users")
    
    If Not rs.EOF Then
        MsgBox "النظام يعمل بنجاح!" & vbCrLf & _
               "المستخدم: " & rs!FullName, vbInformation
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub
'@
    
    Write-Host $vbaCode -ForegroundColor DarkGray
    Write-Host ""
    Write-Host "7. تشغيل النظام:" -ForegroundColor Cyan
    Write-Host "   - اضغط Ctrl+G لفتح Immediate Window" -ForegroundColor White
    Write-Host "   - اكتب: WelcomeToRafea واضغط Enter" -ForegroundColor White
    Write-Host "   - أو اكتب: TestSystem واضغط Enter" -ForegroundColor White
    Write-Host ""
    Write-Host "════════════════════════════════════════" -ForegroundColor Green
    Write-Host "نظام رافع للتطوير العقاري جاهز!" -ForegroundColor Yellow
    Write-Host "════════════════════════════════════════" -ForegroundColor Green
    
}
catch {
    Write-Host "خطأ في فتح Access: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "تأكد من تثبيت Microsoft Access على النظام" -ForegroundColor Yellow
}
