# سكريبت إنشاء الواجهة الأمامية لنظام رافع
# Rafea System Frontend Creation Script

Write-Host "========================================" -ForegroundColor Green
Write-Host "    إنشاء الواجهة الأمامية" -ForegroundColor Yellow
Write-Host "    Creating Frontend Application" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$frontendPath = ".\RafeaSystem\RafeaSystem_FE.accdb"
$backendPath = ".\RafeaSystem\Database\RafeaSystem_BE.accdb"

# التحقق من وجود قاعدة البيانات الخلفية
if (-not (Test-Path $backendPath)) {
    Write-Host "✗ لم يتم العثور على قاعدة البيانات الخلفية" -ForegroundColor Red
    Write-Host "✗ Backend database not found" -ForegroundColor Red
    Write-Host "يرجى تشغيل Create_Database.ps1 أولاً" -ForegroundColor Red
    Write-Host "Please run Create_Database.ps1 first" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج / Press Enter to exit"
    exit
}

try {
    Write-Host "إنشاء الواجهة الأمامية..." -ForegroundColor Cyan
    Write-Host "Creating frontend application..." -ForegroundColor Cyan
    
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # إنشاء قاعدة بيانات الواجهة الأمامية
    $frontendDB = $access.DBEngine.CreateDatabase($frontendPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "✓ تم إنشاء ملف الواجهة الأمامية" -ForegroundColor Green
    Write-Host "✓ Frontend file created" -ForegroundColor Green
    
    # ربط الجداول من قاعدة البيانات الخلفية
    Write-Host "ربط الجداول..." -ForegroundColor Yellow
    Write-Host "Linking tables..." -ForegroundColor Yellow
    
    $tables = @("Users", "Projects", "Customers")
    
    foreach ($table in $tables) {
        try {
            $linkedTable = $frontendDB.CreateTableDef($table)
            $linkedTable.Connect = ";DATABASE=$backendPath"
            $linkedTable.SourceTableName = $table
            $frontendDB.TableDefs.Append($linkedTable)
            Write-Host "  ✓ ربط جدول: $table" -ForegroundColor Green
        }
        catch {
            Write-Host "  ✗ خطأ في ربط جدول: $table" -ForegroundColor Red
        }
    }
    
    $frontendDB.Close()
    
    # فتح الواجهة الأمامية لإضافة النماذج
    $access.OpenCurrentDatabase($frontendPath)
    
    Write-Host "إنشاء النماذج الأساسية..." -ForegroundColor Yellow
    Write-Host "Creating basic forms..." -ForegroundColor Yellow
    
    # إنشاء نموذج تسجيل الدخول
    $loginForm = $access.CreateForm()
    $loginForm.Caption = "تسجيل الدخول - نظام رافع"
    $loginForm.NavigationButtons = $false
    $loginForm.RecordSelectors = $false
    $loginForm.DividingLines = $false
    $loginForm.Modal = $true
    $loginForm.PopUp = $true
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmLogin")
    $access.DoCmd.Close(2, "frmLogin")
    
    Write-Host "  ✓ نموذج تسجيل الدخول" -ForegroundColor Green
    
    # إنشاء النموذج الرئيسي
    $mainForm = $access.CreateForm()
    $mainForm.Caption = "نظام رافع للتطوير العقاري - الواجهة الرئيسية"
    $mainForm.NavigationButtons = $false
    $mainForm.RecordSelectors = $false
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmMain")
    $access.DoCmd.Close(2, "frmMain")
    
    Write-Host "  ✓ النموذج الرئيسي" -ForegroundColor Green
    
    # إضافة الكود الأساسي
    Write-Host "إضافة الكود الأساسي..." -ForegroundColor Yellow
    Write-Host "Adding basic code..." -ForegroundColor Yellow
    
    # إنشاء وحدة الوظائف الأساسية
    $module = $access.Modules.Add("CoreFunctions")
    
    $vbaCode = @"
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول البسيطة
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            LoginUser = True
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في تسجيل الدخول: " & Err.Description, vbCritical
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة عرض رسالة ترحيب
Public Sub ShowWelcomeMessage()
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & _
           "المستخدم: " & CurrentUserFullName & vbCrLf & _
           "النوع: " & CurrentUserType, vbInformation, "مرحباً"
End Sub
"@
    
    # إضافة الكود للوحدة
    $module.InsertText($vbaCode)
    
    Write-Host "  ✓ وحدة الوظائف الأساسية" -ForegroundColor Green
    
    # تعيين نموذج البداية
    $access.CurrentProject.Properties.Add("StartupForm", "frmLogin")
    
    Write-Host "  ✓ تعيين نموذج البداية" -ForegroundColor Green
    
    # حفظ وإغلاق
    $access.DoCmd.Save()
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host ""
    Write-Host "✓ تم إنشاء الواجهة الأمامية بنجاح!" -ForegroundColor Green
    Write-Host "✓ Frontend application created successfully!" -ForegroundColor Green
    Write-Host "المسار: $frontendPath" -ForegroundColor Cyan
    Write-Host "Path: $frontendPath" -ForegroundColor Cyan
}
catch {
    Write-Host "✗ خطأ في إنشاء الواجهة الأمامية: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "✗ Error creating frontend: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "معلومات تسجيل الدخول الافتراضية:" -ForegroundColor Yellow
Write-Host "Default login credentials:" -ForegroundColor Yellow
Write-Host "اسم المستخدم / Username: admin" -ForegroundColor Cyan
Write-Host "كلمة المرور / Password: admin123" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
