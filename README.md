# نظام رافع للتطوير العقاري
# Rafea Real Estate Development System

## 📋 نظرة عامة | Overview

نظام إداري ومحاسبي متكامل مصمم خصيصاً لشركات التطوير العقاري. يوفر النظام إدارة شاملة للمشاريع، المبيعات، المقاولين، الموردين، والعمليات اليومية مع دعم العمل على الشبكة المحلية.

A comprehensive administrative and accounting system designed specifically for real estate development companies. The system provides complete management of projects, sales, contractors, suppliers, and daily operations with local network support.

## 🚀 الميزات الرئيسية | Key Features

### 🏗️ إدارة المشاريع | Project Management
- إدارة شاملة للمشاريع العقارية
- تتبع نسبة الإنجاز والتكاليف
- إدارة الوحدات السكنية والتجارية
- تقارير تفصيلية للمشاريع

### 💰 إدارة المبيعات | Sales Management
- إدارة العملاء والعقود
- نظام الحجوزات والمدفوعات
- تتبع حالة الوحدات (متاح، محجوز، مباع)
- إدارة الأقساط والمتأخرات

### 🔨 إدارة المقاولين | Contractor Management
- إدارة بيانات المقاولين وتخصصاتهم
- نظام المستخلصات والاعتمادات
- تتبع المدفوعات والمستحقات
- تقييم أداء المقاولين

### 📦 إدارة الموردين | Supplier Management
- إدارة بيانات الموردين
- نظام الفواتير والاعتمادات
- إدارة المدفوعات والمستحقات
- تتبع المشتريات والتكاليف

### 🛒 إدارة المشتريات | Procurement Management
- طلبات الشراء والموافقات
- ربط الطلبات بالمشاريع
- تتبع حالة الطلبات
- تقارير المشتريات

### 🔧 إدارة الصيانة | Maintenance Management
- تسجيل أعمال الصيانة (وقائية، إصلاحية، طارئة)
- إدارة التكاليف التشغيلية
- جدولة أعمال الصيانة
- تقارير الصيانة والتكاليف

### ✅ إدارة المهام | Task Management
- نظام المهام اليومية
- تكليف المهام للموظفين
- تتبع الأولويات والمواعيد النهائية
- تقارير الأداء والإنتاجية

### 📊 نظام التقارير | Reporting System
- تقارير شاملة لجميع الوحدات
- خيارات تصفية وبحث متقدمة
- تصدير إلى PDF و Excel
- تقارير مالية وإحصائية

### 👥 إدارة المستخدمين | User Management
- نظام صلاحيات متقدم
- أنواع مستخدمين متعددة (مدير، محاسب، مهندس، مبيعات)
- تسجيل الأنشطة والمراجعة
- أمان متقدم للبيانات

### 🌐 الشبكة المحلية | Network Support
- العمل على شبكة Wi-Fi داخلية
- تقسيم Front-End و Back-End
- دعم تعدد المستخدمين
- نسخ احتياطي تلقائي

## 🛠️ التقنيات المستخدمة | Technologies Used

- **قاعدة البيانات:** Microsoft Access
- **لغة البرمجة:** VBA (Visual Basic for Applications)
- **الواجهة:** نماذج Access مع دعم RTL
- **الشبكة:** Windows File Sharing
- **التقارير:** Access Reports مع تصدير متعدد الصيغ

## 📋 متطلبات النظام | System Requirements

### الحد الأدنى | Minimum Requirements
- **نظام التشغيل:** Windows 10
- **البرامج:** Microsoft Access 2016 أو أحدث
- **الذاكرة:** 4 GB RAM
- **مساحة القرص:** 2 GB
- **الشبكة:** Wi-Fi أو Ethernet

### المستوى الموصى به | Recommended
- **نظام التشغيل:** Windows 11
- **البرامج:** Microsoft Access 2021
- **الذاكرة:** 8 GB RAM
- **مساحة القرص:** 5 GB
- **الشبكة:** Gigabit Ethernet

## 📁 هيكل المشروع | Project Structure

```
RafeaSystem/
├── Database/
│   ├── RafeaSystem_BE.accdb          # قاعدة البيانات الخلفية
│   └── RafeaSystem_FE.accdb          # واجهة المستخدم الأمامية
├── Documentation/
│   ├── Database_Design.md            # تصميم قاعدة البيانات
│   ├── User_Manual.md               # دليل المستخدم
│   ├── Testing_Documentation.md     # دليل الاختبار
│   └── Network_Setup_Guide.md       # دليل إعداد الشبكة
├── Scripts/
│   ├── Create_Database.sql          # سكريبت إنشاء قاعدة البيانات
│   ├── VBA_Core_Functions.vba       # الوظائف الأساسية
│   ├── Network_Management.vba       # إدارة الشبكة
│   └── Form_Codes/                  # أكواد النماذج
├── Config/
│   └── Config.ini                   # ملف التكوين
└── README.md                        # هذا الملف
```

## 🚀 التثبيت والإعداد | Installation & Setup

### 1. إعداد الخادم | Server Setup
```bash
# إنشاء مجلد مشترك
mkdir C:\RafeaSystem\Database
mkdir C:\RafeaSystem\Backups

# نسخ قاعدة البيانات
copy RafeaSystem_BE.accdb C:\RafeaSystem\Database\

# مشاركة المجلد على الشبكة
net share RafeaDB=C:\RafeaSystem\Database /grant:everyone,full
```

### 2. إعداد أجهزة العملاء | Client Setup
```bash
# إنشاء مجلد محلي
mkdir C:\RafeaSystem

# نسخ واجهة المستخدم
copy RafeaSystem_FE.accdb C:\RafeaSystem\

# إنشاء ملف التكوين
copy Config.ini C:\RafeaSystem\
```

### 3. ربط قاعدة البيانات | Database Linking
1. افتح ملف Front-End
2. استخدم Linked Table Manager
3. حدد مسار الخادم: `\\ServerIP\RafeaDB\RafeaSystem_BE.accdb`

## 👤 المستخدم الافتراضي | Default User

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **النوع:** مدير

⚠️ **تحذير:** يُرجى تغيير كلمة المرور الافتراضية فور التثبيت

## 📖 الاستخدام | Usage

### تسجيل الدخول
1. افتح النظام من اختصار سطح المكتب
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط "تسجيل الدخول"

### الواجهة الرئيسية
- **لوحة التحكم:** عرض الإحصائيات الرئيسية
- **قائمة الوحدات:** الوصول لجميع وحدات النظام
- **معلومات المستخدم:** عرض بيانات المستخدم الحالي

### الوحدات الأساسية
- **المشاريع:** إدارة المشاريع والوحدات
- **المبيعات:** إدارة العملاء والعقود
- **المقاولين:** إدارة المقاولين والمستخلصات
- **الموردين:** إدارة الموردين والفواتير
- **المشتريات:** إدارة طلبات الشراء
- **الصيانة:** إدارة أعمال الصيانة
- **المهام:** إدارة المهام اليومية
- **التقارير:** إنشاء وطباعة التقارير

## 🔒 الأمان | Security

### مستويات الأمان
1. **أمان الشبكة:** تشفير Wi-Fi (WPA3)
2. **أمان المجلد:** صلاحيات Windows
3. **أمان التطبيق:** نظام المستخدمين
4. **أمان البيانات:** النسخ الاحتياطي

### أنواع المستخدمين
- **مدير:** جميع الصلاحيات
- **محاسب:** المالية والتقارير
- **مهندس:** المشاريع والصيانة
- **مبيعات:** العملاء والعقود

## 💾 النسخ الاحتياطي | Backup

### النسخ التلقائي
- **يومي:** الساعة 11:00 مساءً
- **الاحتفاظ:** 30 يوم
- **المكان:** مجلد Backups على الخادم

### النسخ اليدوي
```vba
' تشغيل النسخ الاحتياطي يدوياً
Call CreateNetworkBackup()
```

## 📊 التقارير | Reports

### أنواع التقارير
- **تقارير المشاريع:** حالة المشاريع والوحدات
- **تقارير المبيعات:** العملاء والعقود والمدفوعات
- **تقارير المقاولين:** المستخلصات والمدفوعات
- **تقارير الموردين:** الفواتير والمستحقات
- **التقارير المالية:** الإيرادات والمصروفات
- **التقارير الإحصائية:** مؤشرات الأداء

### صيغ التصدير
- **PDF:** للطباعة والأرشفة
- **Excel:** للتحليل والمعالجة
- **معاينة:** للعرض على الشاشة

## 🐛 استكشاف الأخطاء | Troubleshooting

### المشاكل الشائعة
1. **عدم الاتصال بالخادم**
   - فحص اتصال الشبكة
   - التأكد من تشغيل الخادم
   - فحص صلاحيات المجلد

2. **بطء في الأداء**
   - ضغط قاعدة البيانات
   - تحسين الاستعلامات
   - فحص الشبكة

3. **مشاكل تسجيل الدخول**
   - التأكد من صحة البيانات
   - فحص حالة المستخدم
   - إعادة تعيين كلمة المرور

## 📞 الدعم الفني | Technical Support

### للحصول على المساعدة
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-XX-XXX-XXXX
- **الموقع:** www.rafea.com

### ساعات الدعم
- **الأحد - الخميس:** 8:00 ص - 5:00 م
- **الجمعة - السبت:** مغلق

## 📄 الترخيص | License

هذا النظام مطور خصيصاً لشركة رافع للتطوير العقاري. جميع الحقوق محفوظة.

This system is developed specifically for Rafea Real Estate Development Company. All rights reserved.

## 🔄 الإصدارات | Versions

### الإصدار الحالي: 1.0
- **تاريخ الإصدار:** ديسمبر 2024
- **الميزات:** جميع الوحدات الأساسية
- **الحالة:** مستقر للإنتاج

### الإصدارات القادمة
- **1.1:** تحسينات الأداء
- **1.2:** ميزات إضافية للتقارير
- **2.0:** واجهة ويب

## 👨‍💻 فريق التطوير | Development Team

- **مطور النظام:** فريق التطوير - شركة رافع
- **مدير المشروع:** إدارة تقنية المعلومات
- **ضمان الجودة:** فريق الاختبار

---

**© 2024 شركة رافع للتطوير العقاري - جميع الحقوق محفوظة**

**© 2024 Rafea Real Estate Development Company - All Rights Reserved**
