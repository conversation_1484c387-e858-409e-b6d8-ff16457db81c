' كود نموذج إدارة الصيانة والتشغيل
' Maintenance and Operations Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    Me.Caption = "إدارة الصيانة والتشغيل"
    Me.RightToLeft = True
    SetFormPermissions
    RefreshData
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    Me.cmdAddMaintenance.Enabled = CheckPermission("الصيانة", "CanAdd")
    Me.cmdEditMaintenance.Enabled = CheckPermission("الصيانة", "CanEdit")
    Me.cmdDeleteMaintenance.Enabled = CheckPermission("الصيانة", "CanDelete")
    Me.cmdAddOperatingCost.Enabled = CheckPermission("الصيانة", "CanAdd")
    Me.cmdPrint.Enabled = CheckPermission("الصيانة", "CanPrint")
End Sub

Private Sub RefreshData()
    ' تحديث قائمة أعمال الصيانة
    Me.lstMaintenance.RowSource = "SELECT m.MaintenanceID, p.ProjectName, m.MaintenanceType, " & _
                                  "m.Description, m.MaintenanceStatus, m.Cost " & _
                                  "FROM Maintenance m LEFT JOIN Projects p ON m.ProjectID = p.ProjectID " & _
                                  "ORDER BY m.RequestDate DESC"
    Me.lstMaintenance.Requery
    
    ' تحديث قائمة التكاليف التشغيلية
    Me.lstOperatingCosts.RowSource = "SELECT oc.CostID, p.ProjectName, oc.CostType, " & _
                                     "oc.Amount, oc.CostDate " & _
                                     "FROM OperatingCosts oc INNER JOIN Projects p ON oc.ProjectID = p.ProjectID " & _
                                     "ORDER BY oc.CostDate DESC"
    Me.lstOperatingCosts.Requery
End Sub

Private Sub UpdateStatistics()
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إحصائيات الصيانة
    Me.lblTotalMaintenance.Caption = DCount("*", "Maintenance")
    Me.lblPendingMaintenance.Caption = DCount("*", "Maintenance", "MaintenanceStatus = 'مطلوب'")
    Me.lblInProgressMaintenance.Caption = DCount("*", "Maintenance", "MaintenanceStatus = 'قيد التنفيذ'")
    Me.lblCompletedMaintenance.Caption = DCount("*", "Maintenance", "MaintenanceStatus = 'مكتمل'")
    
    ' إجمالي تكاليف الصيانة
    Set rs = db.OpenRecordset("SELECT Sum(Cost) AS TotalMaintenanceCost FROM Maintenance WHERE MaintenanceStatus = 'مكتمل'")
    If Not IsNull(rs!TotalMaintenanceCost) Then
        Me.lblTotalMaintenanceCost.Caption = FormatCurrency(rs!TotalMaintenanceCost)
    Else
        Me.lblTotalMaintenanceCost.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي التكاليف التشغيلية
    Set rs = db.OpenRecordset("SELECT Sum(Amount) AS TotalOperatingCosts FROM OperatingCosts")
    If Not IsNull(rs!TotalOperatingCosts) Then
        Me.lblTotalOperatingCosts.Caption = FormatCurrency(rs!TotalOperatingCosts)
    Else
        Me.lblTotalOperatingCosts.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إدارة أعمال الصيانة
Private Sub cmdAddMaintenance_Click()
    If Not CheckPermission("الصيانة", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة عمل صيانة جديد", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmMaintenanceDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditMaintenance_Click()
    If IsNull(Me.lstMaintenance) Then
        MsgBox "يرجى اختيار عمل صيانة للتعديل", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmMaintenanceDetails", acNormal, , "MaintenanceID = " & Me.lstMaintenance, acFormEdit
End Sub

Private Sub cmdDeleteMaintenance_Click()
    If IsNull(Me.lstMaintenance) Then
        MsgBox "يرجى اختيار عمل صيانة للحذف", vbExclamation
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا العمل؟", vbYesNo + vbCritical) = vbYes Then
        CurrentDb.Execute "DELETE FROM Maintenance WHERE MaintenanceID = " & Me.lstMaintenance
        LogActivity "حذف", "Maintenance", Me.lstMaintenance, "حذف عمل صيانة"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف العمل بنجاح", vbInformation
    End If
End Sub

' إدارة التكاليف التشغيلية
Private Sub cmdAddOperatingCost_Click()
    If Not CheckPermission("الصيانة", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة تكلفة تشغيلية جديدة", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmOperatingCostDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditOperatingCost_Click()
    If IsNull(Me.lstOperatingCosts) Then
        MsgBox "يرجى اختيار تكلفة للتعديل", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmOperatingCostDetails", acNormal, , "CostID = " & Me.lstOperatingCosts, acFormEdit
End Sub

Private Sub cmdDeleteOperatingCost_Click()
    If IsNull(Me.lstOperatingCosts) Then
        MsgBox "يرجى اختيار تكلفة للحذف", vbExclamation
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذه التكلفة؟", vbYesNo + vbCritical) = vbYes Then
        CurrentDb.Execute "DELETE FROM OperatingCosts WHERE CostID = " & Me.lstOperatingCosts
        LogActivity "حذف", "OperatingCosts", Me.lstOperatingCosts, "حذف تكلفة تشغيلية"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف التكلفة بنجاح", vbInformation
    End If
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation
End Sub

Private Sub lstMaintenance_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstMaintenance) Then
        cmdEditMaintenance_Click
    End If
End Sub

Private Sub lstOperatingCosts_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstOperatingCosts) Then
        cmdEditOperatingCost_Click
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Maintenance", 0, "إغلاق نموذج إدارة الصيانة"
End Sub
