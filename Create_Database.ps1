# سكريبت إنشاء قاعدة بيانات نظام رافع للتطوير العقاري
# Rafea Real Estate System Database Creation Script

Write-Host "========================================" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    Rafea Real Estate System" -ForegroundColor Yellow
Write-Host "    Database Creation Script" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# التحقق من وجود Microsoft Access
Write-Host "فحص توفر Microsoft Access..." -ForegroundColor Cyan
Write-Host "Checking Microsoft Access availability..." -ForegroundColor Cyan

try {
    $access = New-Object -ComObject Access.Application
    Write-Host "✓ تم العثور على Microsoft Access" -ForegroundColor Green
    Write-Host "✓ Microsoft Access found" -ForegroundColor Green
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
}
catch {
    Write-Host "✗ لم يتم العثور على Microsoft Access" -ForegroundColor Red
    Write-Host "✗ Microsoft Access not found" -ForegroundColor Red
    Write-Host "يرجى تثبيت Microsoft Access أولاً" -ForegroundColor Red
    Write-Host "Please install Microsoft Access first" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج / Press Enter to exit"
    exit
}

Write-Host ""

# إنشاء قاعدة البيانات الخلفية
Write-Host "إنشاء قاعدة البيانات الخلفية..." -ForegroundColor Cyan
Write-Host "Creating backend database..." -ForegroundColor Cyan

$databasePath = ".\RafeaSystem\Database\RafeaSystem_BE.accdb"

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # إنشاء قاعدة بيانات جديدة
    $db = $access.DBEngine.CreateDatabase($databasePath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "✓ تم إنشاء قاعدة البيانات الأساسية" -ForegroundColor Green
    Write-Host "✓ Basic database created" -ForegroundColor Green
    
    # إنشاء الجداول الأساسية
    Write-Host "إنشاء الجداول..." -ForegroundColor Yellow
    Write-Host "Creating tables..." -ForegroundColor Yellow
    
    # جدول المستخدمين
    $sql = @"
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FullName TEXT(100) NOT NULL,
    UserType TEXT(20) NOT NULL DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME
);
"@
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخدمين" -ForegroundColor Green
    
    # جدول المشاريع
    $sql = @"
CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(200) NOT NULL,
    ProjectLocation TEXT(300),
    ProjectType TEXT(50) DEFAULT 'سكني',
    TotalCost CURRENCY DEFAULT 0,
    StartDate DATETIME,
    ExpectedEndDate DATETIME,
    ActualEndDate DATETIME,
    CompletionPercentage SINGLE DEFAULT 0,
    ProjectStatus TEXT(20) DEFAULT 'قيد التنفيذ',
    Description MEMO,
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "  ✓ جدول المشاريع" -ForegroundColor Green
    
    # جدول العملاء
    $sql = @"
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerName TEXT(100) NOT NULL,
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    NationalID TEXT(20),
    CustomerType TEXT(20) DEFAULT 'فرد',
    Notes MEMO,
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "  ✓ جدول العملاء" -ForegroundColor Green
    
    # إدراج المستخدم الافتراضي
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Yellow
    Write-Host "Inserting initial data..." -ForegroundColor Yellow
    
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
    $db.Execute($sql)
    Write-Host "  ✓ المستخدم الافتراضي: admin/admin123" -ForegroundColor Green
    
    $db.Close()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host ""
    Write-Host "✓ تم إنشاء قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "✓ Database created successfully!" -ForegroundColor Green
    Write-Host "المسار: $databasePath" -ForegroundColor Cyan
    Write-Host "Path: $databasePath" -ForegroundColor Cyan
}
catch {
    Write-Host "✗ خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "✗ Error creating database: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "انتهى إنشاء قاعدة البيانات" -ForegroundColor Yellow
Write-Host "Database creation completed" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Read-Host "اضغط Enter للمتابعة / Press Enter to continue"
