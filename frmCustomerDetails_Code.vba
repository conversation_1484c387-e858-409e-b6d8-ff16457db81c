' كود نموذج تفاصيل العميل
' Customer Details Form Code

Option Compare Database
Option Explicit

Private IsNewRecord As Boolean

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "تفاصيل العميل"
    Me.RightToLeft = True
    
    ' تحديد نوع العملية
    IsNewRecord = (Me.DataEntry = True)
    
    If IsNewRecord Then
        Me.Caption = "إضافة عميل جديد"
        ' تعيين القيم الافتراضية
        Me.cboCustomerType = "فرد"
        Me.txtCreatedDate = Now()
    Else
        Me.Caption = "تفاصيل العميل: " & Me.txtCustomerName
    End If
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث قوائم الاختيار
    UpdateComboBoxes
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم ونوع العملية
    Dim CanEdit As Boolean
    
    If IsNewRecord Then
        CanEdit = CheckPermission("المبيعات", "CanAdd")
    Else
        CanEdit = CheckPermission("المبيعات", "CanEdit")
    End If
    
    ' تعيين حالة الحقول
    Me.txtCustomerName.Enabled = CanEdit
    Me.txtPhone.Enabled = CanEdit
    Me.txtMobile.Enabled = CanEdit
    Me.txtEmail.Enabled = CanEdit
    Me.txtAddress.Enabled = CanEdit
    Me.txtNationalID.Enabled = CanEdit
    Me.cboCustomerType.Enabled = CanEdit
    Me.txtNotes.Enabled = CanEdit
    
    ' أزرار الحفظ والإلغاء
    Me.cmdSave.Enabled = CanEdit
    Me.cmdCancel.Enabled = True
    Me.cmdClose.Enabled = True
End Sub

Private Sub UpdateComboBoxes()
    ' تحديث قائمة أنواع العملاء
    Me.cboCustomerType.RowSource = "'فرد';'شركة'"
End Sub

Private Sub cmdSave_Click()
    ' حفظ بيانات العميل
    On Error GoTo ErrorHandler
    
    ' التحقق من صحة البيانات
    If Not ValidateData() Then
        Exit Sub
    End If
    
    ' حفظ البيانات
    If Me.Dirty Then
        Me.Dirty = False
        
        If IsNewRecord Then
            ' تسجيل النشاط للإضافة
            LogActivity "إضافة", "Customers", Me.txtCustomerID, "إضافة عميل جديد: " & Me.txtCustomerName
            MsgBox "تم إضافة العميل بنجاح", vbInformation, "تم الحفظ"
        Else
            ' تسجيل النشاط للتعديل
            LogActivity "تعديل", "Customers", Me.txtCustomerID, "تعديل عميل: " & Me.txtCustomerName
            MsgBox "تم حفظ التعديلات بنجاح", vbInformation, "تم الحفظ"
        End If
        
        ' إغلاق النموذج
        DoCmd.Close acForm, Me.Name
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء حفظ البيانات: " & Err.Description, vbCritical, "خطأ"
End Sub

Private Function ValidateData() As Boolean
    ValidateData = True
    
    ' التحقق من اسم العميل
    If Not ValidateRequired(Me.txtCustomerName, "اسم العميل") Then
        Me.txtCustomerName.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من نوع العميل
    If IsNull(Me.cboCustomerType) Then
        MsgBox "يجب اختيار نوع العميل", vbExclamation, "بيانات مطلوبة"
        Me.cboCustomerType.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من رقم الهاتف أو الجوال (واحد منهما على الأقل)
    If IsNull(Me.txtPhone) And IsNull(Me.txtMobile) Then
        MsgBox "يجب إدخال رقم الهاتف أو الجوال على الأقل", vbExclamation, "بيانات مطلوبة"
        Me.txtPhone.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من صحة رقم الهاتف
    If Not ValidatePhone(Me.txtPhone & "") Then
        Me.txtPhone.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من صحة رقم الجوال
    If Not ValidatePhone(Me.txtMobile & "") Then
        Me.txtMobile.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من صحة البريد الإلكتروني
    If Not ValidateEmail(Me.txtEmail & "") Then
        Me.txtEmail.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من الرقم الوطني (إذا كان العميل فرد)
    If Me.cboCustomerType = "فرد" And Not IsNull(Me.txtNationalID) Then
        If Len(Me.txtNationalID) <> 10 Then
            MsgBox "الرقم الوطني يجب أن يكون 10 أرقام", vbExclamation, "خطأ في البيانات"
            Me.txtNationalID.SetFocus
            ValidateData = False
            Exit Function
        End If
        
        ' التحقق من عدم تكرار الرقم الوطني
        Dim ExistingCustomer As String
        If IsNewRecord Then
            ExistingCustomer = "CustomerID <> 0"
        Else
            ExistingCustomer = "CustomerID <> " & Me.txtCustomerID
        End If
        
        If DCount("*", "Customers", "NationalID = '" & Me.txtNationalID & "' AND " & ExistingCustomer) > 0 Then
            MsgBox "هذا الرقم الوطني مسجل لعميل آخر", vbExclamation, "رقم مكرر"
            Me.txtNationalID.SetFocus
            ValidateData = False
            Exit Function
        End If
    End If
End Function

Private Sub cmdCancel_Click()
    ' إلغاء التعديلات
    If Me.Dirty Then
        If MsgBox("هل تريد إلغاء التعديلات؟", vbYesNo + vbQuestion, "إلغاء التعديلات") = vbYes Then
            Me.Undo
            DoCmd.Close acForm, Me.Name
        End If
    Else
        DoCmd.Close acForm, Me.Name
    End If
End Sub

Private Sub cmdClose_Click()
    ' إغلاق النموذج
    If Me.Dirty Then
        Select Case MsgBox("هل تريد حفظ التعديلات قبل الإغلاق؟", vbYesNoCancel + vbQuestion, "حفظ التعديلات")
            Case vbYes
                cmdSave_Click
            Case vbNo
                Me.Undo
                DoCmd.Close acForm, Me.Name
            Case vbCancel
                ' لا تفعل شيئاً
        End Select
    Else
        DoCmd.Close acForm, Me.Name
    End If
End Sub

Private Sub cboCustomerType_AfterUpdate()
    ' تعديل الحقول المطلوبة حسب نوع العميل
    If Me.cboCustomerType = "شركة" Then
        Me.lblNationalID.Caption = "رقم السجل التجاري:"
        Me.txtNationalID.InputMask = ""
    Else
        Me.lblNationalID.Caption = "الرقم الوطني:"
        Me.txtNationalID.InputMask = "0000000000"
    End If
End Sub

Private Sub txtNationalID_BeforeUpdate(Cancel As Integer)
    ' التحقق من الرقم الوطني أثناء الإدخال
    If Me.cboCustomerType = "فرد" And Not IsNull(Me.txtNationalID) Then
        If Len(Me.txtNationalID) <> 10 Then
            MsgBox "الرقم الوطني يجب أن يكون 10 أرقام", vbExclamation, "خطأ في البيانات"
            Cancel = True
            Exit Sub
        End If
        
        ' التحقق من أن جميع الأرقام متشابهة (رقم غير صحيح)
        Dim i As Integer
        Dim AllSame As Boolean
        AllSame = True
        For i = 2 To Len(Me.txtNationalID)
            If Mid(Me.txtNationalID, i, 1) <> Mid(Me.txtNationalID, 1, 1) Then
                AllSame = False
                Exit For
            End If
        Next i
        
        If AllSame Then
            MsgBox "الرقم الوطني غير صحيح", vbExclamation, "خطأ في البيانات"
            Cancel = True
        End If
    End If
End Sub

Private Sub txtEmail_BeforeUpdate(Cancel As Integer)
    ' التحقق من البريد الإلكتروني أثناء الإدخال
    If Not ValidateEmail(Me.txtEmail & "") Then
        Cancel = True
    End If
End Sub

Private Sub txtPhone_BeforeUpdate(Cancel As Integer)
    ' التحقق من رقم الهاتف أثناء الإدخال
    If Not ValidatePhone(Me.txtPhone & "") Then
        Cancel = True
    End If
End Sub

Private Sub txtMobile_BeforeUpdate(Cancel As Integer)
    ' التحقق من رقم الجوال أثناء الإدخال
    If Not ValidatePhone(Me.txtMobile & "") Then
        Cancel = True
    End If
End Sub

Private Sub cmdViewContracts_Click()
    ' عرض عقود العميل
    If IsNewRecord Then
        MsgBox "يجب حفظ العميل أولاً", vbExclamation, "لم يتم الحفظ"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContracts", acNormal, , "CustomerID = " & Me.txtCustomerID
End Sub

Private Sub Form_BeforeUpdate(Cancel As Integer)
    ' التحقق من البيانات قبل الحفظ
    If Not ValidateData() Then
        Cancel = True
    End If
End Sub

Private Sub Form_AfterUpdate()
    ' تحديث العنوان بعد الحفظ
    If Not IsNewRecord Then
        Me.Caption = "تفاصيل العميل: " & Me.txtCustomerName
    End If
End Sub

Private Sub Form_KeyDown(KeyCode As Integer, Shift As Integer)
    ' اختصارات لوحة المفاتيح
    Select Case KeyCode
        Case vbKeyS
            If Shift = acCtrlMask Then ' Ctrl+S للحفظ
                cmdSave_Click
                KeyCode = 0
            End If
        Case vbKeyEscape ' Escape للإلغاء
            cmdCancel_Click
            KeyCode = 0
    End Select
End Sub
