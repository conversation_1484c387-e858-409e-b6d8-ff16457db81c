# إنشاء نظام رافع - النسخة العملية
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    إنشاء النسخة العملية" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Working.accdb"

Write-Host "إنشاء نظام رافع العملي..." -ForegroundColor Yellow
Write-Host "المسار: $systemPath" -ForegroundColor Cyan

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true

    Write-Host "✓ تم فتح Microsoft Access" -ForegroundColor Green

    # إنشاء قاعدة بيانات جديدة
    $access.NewCurrentDatabase($systemPath)

    Write-Host "✓ تم إنشاء قاعدة البيانات" -ForegroundColor Green

    # الحصول على قاعدة البيانات الحالية
    $db = $access.CurrentDb()

    Write-Host "إنشاء الجداول الأساسية..." -ForegroundColor Yellow

    # إنشاء جدول المستخدمين
    $sql = "CREATE TABLE Users (" +
           "UserID COUNTER PRIMARY KEY, " +
           "Username TEXT(50), " +
           "Password TEXT(50), " +
           "FullName TEXT(100), " +
           "UserType TEXT(20), " +
           "IsActive YESNO, " +
           "CreatedDate DATETIME" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخدمين" -ForegroundColor Green

    # إنشاء جدول المشاريع
    $sql = "CREATE TABLE Projects (" +
           "ProjectID COUNTER PRIMARY KEY, " +
           "ProjectName TEXT(200), " +
           "ProjectLocation TEXT(300), " +
           "ProjectType TEXT(50), " +
           "TotalCost CURRENCY, " +
           "CompletionPercentage SINGLE, " +
           "ProjectStatus TEXT(20), " +
           "Description MEMO, " +
           "CreatedDate DATETIME" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المشاريع" -ForegroundColor Green

    # إنشاء جدول العملاء
    $sql = "CREATE TABLE Customers (" +
           "CustomerID COUNTER PRIMARY KEY, " +
           "CustomerName TEXT(100), " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "Email TEXT(100), " +
           "Address TEXT(300), " +
           "CustomerType TEXT(20), " +
           "CreatedDate DATETIME" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول العملاء" -ForegroundColor Green

    # إنشاء جدول الوحدات
    $sql = "CREATE TABLE Units (" +
           "UnitID COUNTER PRIMARY KEY, " +
           "ProjectID LONG, " +
           "UnitNumber TEXT(20), " +
           "UnitType TEXT(50), " +
           "Area SINGLE, " +
           "UnitPrice CURRENCY, " +
           "UnitStatus TEXT(20)" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الوحدات" -ForegroundColor Green

    # إنشاء جدول المقاولين
    $sql = "CREATE TABLE Contractors (" +
           "ContractorID COUNTER PRIMARY KEY, " +
           "ContractorName TEXT(100), " +
           "CompanyName TEXT(150), " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "Specialization TEXT(100), " +
           "IsActive YESNO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المقاولين" -ForegroundColor Green

    # إنشاء جدول الموردين
    $sql = "CREATE TABLE Suppliers (" +
           "SupplierID COUNTER PRIMARY KEY, " +
           "SupplierName TEXT(100), " +
           "CompanyName TEXT(150), " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "SupplierType TEXT(50), " +
           "IsActive YESNO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الموردين" -ForegroundColor Green

    Write-Host "إدراج البيانات التجريبية..." -ForegroundColor Yellow

    # إدراج المستخدمين
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive, CreatedDate) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير', True, Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive, CreatedDate) VALUES ('accountant', '123456', 'المحاسب الرئيسي', 'محاسب', True, Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive, CreatedDate) VALUES ('engineer', '123456', 'المهندس المشرف', 'مهندس', True, Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive, CreatedDate) VALUES ('sales', '123456', 'موظف المبيعات', 'مبيعات', True, Now());"
    $db.Execute($sql)

    Write-Host "  ✓ المستخدمين الأساسيين" -ForegroundColor Green

    # إدراج المشاريع
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedDate) VALUES ('مشروع الواحة السكني', 'الرياض - حي النرجس', 'سكني', 5000000, 75, 'قيد التنفيذ', 'مشروع سكني متكامل يضم 120 وحدة سكنية', Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedDate) VALUES ('برج التجارة المركزي', 'جدة - الكورنيش', 'تجاري', 12000000, 45, 'قيد التنفيذ', 'برج تجاري يضم مكاتب ومحلات تجارية', Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedDate) VALUES ('مجمع الأعمال الذكي', 'الدمام - الخبر', 'إداري', 8000000, 90, 'قيد التنفيذ', 'مجمع إداري حديث بتقنيات ذكية', Now());"
    $db.Execute($sql)

    Write-Host "  ✓ المشاريع التجريبية" -ForegroundColor Green

    # إدراج العملاء
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address, CreatedDate) VALUES ('أحمد محمد السعيد', '011-4567890', '0501234567', '<EMAIL>', 'فرد', 'الرياض - حي الملك فهد', Now());"
    $db.Execute($sql)

    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address, CreatedDate) VALUES ('شركة الخليج للاستثمار', '012-9876543', '0559876543', '<EMAIL>', 'شركة', 'جدة - شارع التحلية', Now());"
    $db.Execute($sql)

    Write-Host "  ✓ العملاء التجريبيين" -ForegroundColor Green

    # إدراج الوحدات
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-101', 'شقة', 120, 450000, 'متاح');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-102', 'شقة', 140, 520000, 'محجوز');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (2, 'OF-301', 'مكتب', 80, 320000, 'متاح');"
    $db.Execute($sql)

    Write-Host "  ✓ الوحدات التجريبية" -ForegroundColor Green

    # إدراج المقاولين
    $sql = "INSERT INTO Contractors (ContractorName, CompanyName, Phone, Mobile, Specialization, IsActive) VALUES ('محمد أحمد البناء', 'شركة البناء المتقدم', '011-7777777', '0507777777', 'أعمال الخرسانة والحديد', True);"
    $db.Execute($sql)

    $sql = "INSERT INTO Contractors (ContractorName, CompanyName, Phone, Mobile, Specialization, IsActive) VALUES ('سعد علي الكهرباء', 'مؤسسة الكهرباء الحديثة', '012-8888888', '0508888888', 'الأعمال الكهربائية', True);"
    $db.Execute($sql)

    Write-Host "  ✓ المقاولين التجريبيين" -ForegroundColor Green

    # إدراج الموردين
    $sql = "INSERT INTO Suppliers (SupplierName, CompanyName, Phone, Mobile, SupplierType, IsActive) VALUES ('خالد محمد التوريد', 'شركة مواد البناء المتحدة', '011-9999999', '0509999999', 'مواد البناء', True);"
    $db.Execute($sql)

    Write-Host "  ✓ الموردين التجريبيين" -ForegroundColor Green

    Write-Host "إنشاء وحدات VBA..." -ForegroundColor Yellow

    # إنشاء وحدة النظام الأساسية
    $module = $access.Modules.Add("RafeaSystem")

    $vbaCode = @'
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String

    On Error GoTo ErrorHandler

    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)

    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType

            LoginUser = True

            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType & vbCrLf & _
                   "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & _
                   "الوقت: " & Format(Time, "hh:nn AM/PM"), vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function

ErrorHandler:
    MsgBox "خطأ في تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة عرض لوحة التحكم
Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String

    Set db = CurrentDb

    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم الرئيسية" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf

    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم: " & CurrentUserFullName & vbCrLf
        msg = msg & "النوع: " & CurrentUserType & vbCrLf
    End If

    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & vbCrLf

    msg = msg & "═══ إحصائيات النظام ═══" & vbCrLf

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Contractors WHERE IsActive = True")
    msg = msg & "المقاولين النشطين: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Suppliers WHERE IsActive = True")
    msg = msg & "الموردين النشطين: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close

    msg = msg & vbCrLf & "═══ وحدات النظام ═══" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات" & vbCrLf
    msg = msg & "✓ إدارة العملاء والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة المقاولين" & vbCrLf
    msg = msg & "✓ إدارة الموردين" & vbCrLf
    msg = msg & "✓ التقارير الشاملة" & vbCrLf

    msg = msg & vbCrLf & "© 2024 شركة رافع للتطوير العقاري"

    MsgBox msg, vbInformation, "لوحة التحكم"

    Set rs = Nothing
    Set db = Nothing
End Sub

' وظيفة عرض المشاريع
Public Sub ShowProjects()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String

    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Projects ORDER BY ProjectName")

    msg = "═══ قائمة المشاريع ═══" & vbCrLf & vbCrLf

    Do While Not rs.EOF
        msg = msg & "• " & rs!ProjectName & vbCrLf
        msg = msg & "  الموقع: " & rs!ProjectLocation & vbCrLf
        msg = msg & "  النوع: " & rs!ProjectType & vbCrLf
        msg = msg & "  التكلفة: " & Format(rs!TotalCost, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  نسبة الإنجاز: " & rs!CompletionPercentage & "%" & vbCrLf
        msg = msg & "  الحالة: " & rs!ProjectStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    MsgBox msg, vbInformation, "المشاريع"
End Sub

' وظيفة عرض العملاء
Public Sub ShowCustomers()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String

    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Customers ORDER BY CustomerName")

    msg = "═══ قائمة العملاء ═══" & vbCrLf & vbCrLf

    Do While Not rs.EOF
        msg = msg & "• " & rs!CustomerName & vbCrLf
        msg = msg & "  النوع: " & rs!CustomerType & vbCrLf
        If Not IsNull(rs!Mobile) Then msg = msg & "  الجوال: " & rs!Mobile & vbCrLf
        If Not IsNull(rs!Email) Then msg = msg & "  البريد: " & rs!Email & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop

    rs.Close
    Set rs = Nothing
    Set db = Nothing

    MsgBox msg, vbInformation, "العملاء"
End Sub

' وظيفة عرض معلومات النظام
Public Sub ShowSystemInfo()
    MsgBox "════════════════════════════════════════" & vbCrLf & _
           "    نظام رافع للتطوير العقاري" & vbCrLf & _
           "    الإصدار 1.0" & vbCrLf & _
           "════════════════════════════════════════" & vbCrLf & vbCrLf & _
           "نظام إداري ومحاسبي متكامل لشركات التطوير العقاري" & vbCrLf & vbCrLf & _
           "الميزات الرئيسية:" & vbCrLf & _
           "✓ إدارة شاملة للمشاريع العقارية" & vbCrLf & _
           "✓ نظام مبيعات متكامل" & vbCrLf & _
           "✓ إدارة المقاولين والمستخلصات" & vbCrLf & _
           "✓ إدارة الموردين والفواتير" & vbCrLf & _
           "✓ نظام التقارير الشامل" & vbCrLf & _
           "✓ نظام صلاحيات متقدم" & vbCrLf & _
           "✓ دعم الشبكة المحلية" & vbCrLf & vbCrLf & _
           "© 2024 شركة رافع للتطوير العقاري", vbInformation, "معلومات النظام"
End Sub
'@

    $module.InsertText($vbaCode)
    Write-Host "  ✓ وحدة النظام الأساسية" -ForegroundColor Green

    # حفظ جميع التغييرات
    $access.DoCmd.Save()

    Write-Host ""
    Write-Host "✓ تم إنشاء نظام رافع بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $systemPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "المدير: admin / admin123" -ForegroundColor Cyan
    Write-Host "المحاسب: accountant / 123456" -ForegroundColor Cyan
    Write-Host "المهندس: engineer / 123456" -ForegroundColor Cyan
    Write-Host "المبيعات: sales / 123456" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "الأوامر المتاحة (Ctrl+G):" -ForegroundColor Yellow
    Write-Host "LoginUser(""admin"", ""admin123"")" -ForegroundColor Cyan
    Write-Host "ShowDashboard" -ForegroundColor Cyan
    Write-Host "ShowProjects" -ForegroundColor Cyan
    Write-Host "ShowCustomers" -ForegroundColor Cyan
    Write-Host "ShowSystemInfo" -ForegroundColor Cyan

}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "نظام رافع للتطوير العقاري جاهز!" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green