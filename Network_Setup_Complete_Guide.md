# دليل إعداد الشبكة الشامل - نظام رافع للتطوير العقاري
# Complete Network Setup Guide - Rafea Real Estate System

## 📋 جدول المحتويات

1. [مقدمة](#مقدمة)
2. [متطلبات الشبكة](#متطلبات-الشبكة)
3. [إعد<PERSON> الخادم الرئيسي](#إعداد-الخادم-الرئيسي)
4. [إعداد أجهزة العملاء](#إعداد-أجهزة-العملاء)
5. [اختبار الاتصال](#اختبار-الاتصال)
6. [استكشاف الأخطاء](#استكشاف-الأخطاء)
7. [الأمان والحماية](#الأمان-والحماية)

---

## 🌐 مقدمة

يدعم **نظام رافع للتطوير العقاري** العمل على الشبكة المحلية (LAN/Wi-Fi) مما يتيح لعدة مستخدمين الوصول للنظام في نفس الوقت. يستخدم النظام تقنية **Front-End/Back-End** حيث تكون قاعدة البيانات على خادم مركزي والواجهات على أجهزة المستخدمين.

### مزايا العمل على الشبكة:
- **وصول متزامن** لعدة مستخدمين (حتى 10 مستخدمين)
- **مشاركة البيانات** في الوقت الفعلي
- **نسخ احتياطي مركزي** محمي
- **إدارة مركزية** للصلاحيات والتحديثات
- **أداء أفضل** مع توزيع الحمولة

---

## 💻 متطلبات الشبكة

### الخادم الرئيسي (Server):
- **نظام التشغيل:** Windows 10 Pro أو Windows Server 2016+
- **المعالج:** Intel Core i5 أو AMD Ryzen 5 (أو أفضل)
- **الذاكرة:** 8 GB RAM كحد أدنى (16 GB مُفضل)
- **التخزين:** 500 GB مساحة فارغة (SSD مُفضل)
- **الشبكة:** Gigabit Ethernet أو Wi-Fi 6
- **Microsoft Access:** 2016 أو أحدث

### أجهزة العملاء (Clients):
- **نظام التشغيل:** Windows 10 أو أحدث
- **المعالج:** Intel Core i3 أو AMD Ryzen 3 (أو أفضل)
- **الذاكرة:** 4 GB RAM كحد أدنى (8 GB مُفضل)
- **الشبكة:** Fast Ethernet أو Wi-Fi 5+
- **Microsoft Access:** 2016 أو أحدث

### معدات الشبكة:
- **راوتر Wi-Fi:** يدعم 802.11ac أو أحدث
- **سويتش شبكة:** Gigabit Ethernet (للاتصال السلكي)
- **كابلات شبكة:** Cat 6 أو أفضل
- **UPS:** لحماية الخادم من انقطاع الكهرباء

---

## 🖥️ إعداد الخادم الرئيسي

### الخطوة 1: إعداد مجلد النظام

1. **أنشئ مجلد النظام الرئيسي:**
   ```
   C:\RafeaSystem_Server\
   ```

2. **أنشئ المجلدات الفرعية:**
   ```
   C:\RafeaSystem_Server\Database\
   C:\RafeaSystem_Server\Backups\
   C:\RafeaSystem_Server\Reports\
   C:\RafeaSystem_Server\Logs\
   ```

3. **انسخ ملفات النظام:**
   - `RafeaSystem_Simple.accdb` → `Database\`
   - `Config.ini` → المجلد الرئيسي
   - `RafeaSystem_VBA_Code.vba` → المجلد الرئيسي

### الخطوة 2: مشاركة المجلد على الشبكة

1. **انقر بالزر الأيمن** على مجلد `RafeaSystem_Server`
2. **اختر Properties**
3. **انتقل لتبويب Sharing**
4. **اضغط Advanced Sharing**
5. **فعّل "Share this folder"**
6. **اسم المشاركة:** `RafeaDB`
7. **اضغط Permissions:**
   - **أضف "Everyone":** Read/Write
   - **أضف "Authenticated Users":** Full Control

### الخطوة 3: إعداد صلاحيات الأمان

1. **انتقل لتبويب Security**
2. **اضغط Edit**
3. **أضف المجموعات التالية:**
   - **Users:** Read & Execute, Write
   - **Administrators:** Full Control
   - **Network Service:** Read & Execute

### الخطوة 4: إعداد الجدار الناري

1. **افتح Windows Defender Firewall**
2. **اختر "Allow an app or feature through Windows Defender Firewall"**
3. **اضغط "Change Settings"**
4. **فعّل العناصر التالية:**
   - **File and Printer Sharing**
   - **Microsoft Access**
   - **Network Discovery**

### الخطوة 5: إعداد النسخ الاحتياطي التلقائي

1. **افتح Task Scheduler**
2. **اختر "Create Basic Task"**
3. **اسم المهمة:** "Rafea Daily Backup"
4. **التكرار:** Daily
5. **الوقت:** 11:00 PM
6. **الإجراء:** Start a program
7. **البرنامج:**
   ```cmd
   xcopy "C:\RafeaSystem_Server\Database\*.accdb" "C:\RafeaSystem_Server\Backups\" /Y /D
   ```

---

## 💻 إعداد أجهزة العملاء

### الخطوة 1: تثبيت المتطلبات

1. **ثبّت Microsoft Access 2016** أو أحدث
2. **تأكد من تفعيل الترخيص**
3. **قم بتحديث Windows** لآخر إصدار
4. **ثبّت .NET Framework 4.7.2** أو أحدث

### الخطوة 2: اختبار الاتصال بالخادم

1. **افتح File Explorer**
2. **اكتب في شريط العنوان:**
   ```
   \\[اسم_الخادم]\RafeaDB
   ```
   أو
   ```
   \\[IP_الخادم]\RafeaDB
   ```
   مثال: `\\RAFEA-SERVER\RafeaDB` أو `\\*************\RafeaDB`

3. **أدخل بيانات المستخدم** إذا طُلب منك
4. **تأكد من ظهور مجلدات النظام**

### الخطوة 3: إنشاء الواجهة الأمامية

1. **افتح Microsoft Access**
2. **اختر "Blank Database"**
3. **احفظ الملف في:**
   ```
   C:\Users\<USER>\Desktop\RafeaSystem_Client.accdb
   ```

4. **اربط الجداول من الخادم:**
   - اذهب إلى **External Data > Access**
   - اختر **"Link to the data source by creating a linked table"**
   - تصفح إلى: `\\[الخادم]\RafeaDB\Database\RafeaSystem_Simple.accdb`
   - اختر **جميع الجداول** للربط
   - اضغط **OK**

### الخطوة 4: إضافة كود VBA

1. **في Access، اضغط Alt+F11** لفتح VBA Editor
2. **اذهب إلى Insert > Module**
3. **انسخ محتوى ملف** `RafeaSystem_VBA_Code.vba`
4. **الصق الكود في النافذة**
5. **احفظ الملف (Ctrl+S)**

### الخطوة 5: إنشاء اختصار سطح المكتب

1. **انقر بالزر الأيمن على سطح المكتب**
2. **اختر New > Shortcut**
3. **اكتب المسار:**
   ```
   "C:\Users\<USER>\Desktop\RafeaSystem_Client.accdb"
   ```
4. **اسم الاختصار:** "نظام رافع للتطوير العقاري"

---

## 🧪 اختبار الاتصال

### اختبار الشبكة الأساسي:

1. **اختبار Ping من جهاز العميل:**
   ```cmd
   ping [IP_الخادم]
   ```
   مثال: `ping *************`

2. **اختبار الوصول للمجلد المشترك:**
   ```cmd
   dir \\[الخادم]\RafeaDB
   ```

3. **اختبار فتح قاعدة البيانات:**
   - افتح Access على جهاز العميل
   - حاول فتح الملف من الخادم مباشرة

### اختبار النظام:

1. **تسجيل الدخول من عدة أجهزة:**
   ```vba
   LoginUser("admin", "admin123")
   ```

2. **إدخال بيانات تجريبية من جهاز واحد**

3. **التحقق من ظهور البيانات على الأجهزة الأخرى:**
   ```vba
   ShowMainDashboard
   ```

4. **اختبار التقارير والطباعة**

### مؤشرات النجاح:
- ✅ جميع الأجهزة تصل للخادم
- ✅ البيانات تظهر على جميع الأجهزة
- ✅ لا توجد رسائل خطأ
- ✅ الأداء مقبول (أقل من 3 ثوان لفتح النماذج)

---

## 🔧 استكشاف الأخطاء

### المشاكل الشائعة والحلول:

#### 1. عدم الوصول للخادم
**الأعراض:** رسالة "Network path not found"

**الحلول:**
```cmd
# فحص الاتصال
ping [IP_الخادم]

# فحص مشاركة الملفات
net view \\[الخادم]

# إعادة تشغيل خدمات الشبكة
net stop server
net start server
```

#### 2. بطء في الأداء
**الأعراض:** بطء في فتح النماذج والتقارير

**الحلول:**
- ضغط قاعدة البيانات شهرياً
- تحسين الاستعلامات
- ترقية معدات الشبكة إلى Gigabit
- تقليل عدد المستخدمين المتزامنين

#### 3. تعارض في البيانات
**الأعراض:** رسائل "Record is locked by another user"

**الحلول:**
- إغلاق النماذج غير المستخدمة
- تجنب فتح نفس السجل من عدة أجهزة
- إعادة تشغيل Access على جميع الأجهزة
- فحص اتصال الشبكة

#### 4. انقطاع الاتصال
**الأعراض:** فقدان الاتصال أثناء العمل

**الحلول:**
- فحص كابلات الشبكة
- إعادة تشغيل الراوتر/السويتش
- تحديث تعريفات كرت الشبكة
- فحص إعدادات توفير الطاقة

---

## 🛡️ الأمان والحماية

### حماية البيانات:

1. **كلمات مرور قوية:**
   - 8 أحرف على الأقل
   - مزيج من الأرقام والحروف والرموز
   - تغيير دوري كل 3 أشهر
   - عدم استخدام كلمات مرور مكررة

2. **تشفير قاعدة البيانات:**
   ```vba
   ' في VBA Editor
   DBEngine.CompactDatabase "source.accdb", "encrypted.accdb", , , ";pwd=كلمة_مرور_قوية"
   ```

3. **صلاحيات الملفات:**
   - قراءة/كتابة للمستخدمين المصرحين فقط
   - منع الحذف للمستخدمين العاديين
   - Full Control للمديرين فقط

### حماية الشبكة:

1. **إعدادات الراوتر:**
   - تغيير كلمة مرور الراوتر الافتراضية
   - تفعيل WPA3 أو WPA2 للشبكة اللاسلكية
   - إخفاء اسم الشبكة (SSID)
   - تفعيل MAC Address Filtering

2. **جدار ناري متقدم:**
   - حجب المنافذ غير الضرورية
   - السماح لـ Access والخدمات المطلوبة فقط
   - مراقبة حركة البيانات

3. **مراقبة الوصول:**
   - سجل دخول/خروج المستخدمين
   - مراقبة العمليات الحساسة
   - تنبيهات للأنشطة المشبوهة

### النسخ الاحتياطي الآمن:

1. **نسخ متعددة:**
   - نسخة يومية على الخادم المحلي
   - نسخة أسبوعية على قرص خارجي
   - نسخة شهرية في موقع منفصل

2. **تشفير النسخ:**
   ```cmd
   # استخدام 7-Zip للضغط والتشفير
   7z a -p[كلمة_المرور] backup.7z RafeaSystem_Simple.accdb
   ```

3. **اختبار الاستعادة:**
   - اختبار شهري لاستعادة النسخ
   - توثيق إجراءات الاستعادة
   - تدريب الفريق على الاستعادة

---

## 📞 الدعم الفني

### للحصول على المساعدة:

1. **راجع هذا الدليل** والوثائق المرفقة
2. **فحص سجلات الأخطاء** في النظام
3. **اتصل بمدير الشبكة** المحلي
4. **تواصل مع الدعم الفني:**

**شركة رافع للتطوير العقاري**
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-11-1234567
- **ساعات العمل:** الأحد - الخميس، 8 صباحاً - 5 مساءً

### معلومات مطلوبة عند الاتصال:
- وصف المشكلة بالتفصيل
- رسائل الخطأ (إن وجدت)
- خطوات إعادة إنتاج المشكلة
- معلومات النظام والشبكة
- عدد المستخدمين المتأثرين

---

**© 2024 شركة رافع للتطوير العقاري - جميع الحقوق محفوظة**

*هذا الدليل يغطي إعداد الشبكة الشامل. للحصول على دعم فني متخصص أو تدريب متقدم، يرجى الاتصال بفريق الدعم.*
