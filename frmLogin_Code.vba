' كود نموذج تسجيل الدخول
' Login Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "نظام رافع للتطوير العقاري - تسجيل الدخول"
    Me.Modal = True
    Me.PopUp = True
    
    ' تعيين التركيز على حقل اسم المستخدم
    Me.txtUsername.SetFocus
    
    ' إخفاء شريط التنقل
    Me.NavigationButtons = False
    Me.RecordSelectors = False
    Me.DividingLines = False
    
    ' تعيين اتجاه النص من اليمين لليسار
    Me.RightToLeft = True
End Sub

Private Sub cmdLogin_Click()
    Dim Username As String
    Dim Password As String
    
    ' التحقق من إدخال البيانات
    If Len(Trim(Me.txtUsername & "")) = 0 Then
        MsgBox "يرجى إدخال اسم المستخدم", vbExclamation, "بيانات مطلوبة"
        Me.txtUsername.SetFocus
        Exit Sub
    End If
    
    If Len(Trim(Me.txtPassword & "")) = 0 Then
        MsgBox "يرجى إدخال كلمة المرور", vbExclamation, "بيانات مطلوبة"
        Me.txtPassword.SetFocus
        Exit Sub
    End If
    
    Username = Trim(Me.txtUsername)
    Password = Trim(Me.txtPassword)
    
    ' محاولة تسجيل الدخول
    If LoginUser(Username, Password) Then
        ' إخفاء نموذج تسجيل الدخول
        Me.Visible = False
        
        ' فتح النموذج الرئيسي
        DoCmd.OpenForm "frmMain"
        
        ' إغلاق نموذج تسجيل الدخول
        DoCmd.Close acForm, Me.Name
    Else
        ' مسح كلمة المرور وإعادة التركيز
        Me.txtPassword = ""
        Me.txtUsername.SetFocus
    End If
End Sub

Private Sub cmdCancel_Click()
    ' إغلاق التطبيق
    Application.Quit
End Sub

Private Sub txtUsername_KeyPress(KeyAscii As Integer)
    ' الانتقال إلى كلمة المرور عند الضغط على Enter
    If KeyAscii = 13 Then
        Me.txtPassword.SetFocus
        KeyAscii = 0
    End If
End Sub

Private Sub txtPassword_KeyPress(KeyAscii As Integer)
    ' تسجيل الدخول عند الضغط على Enter
    If KeyAscii = 13 Then
        cmdLogin_Click
        KeyAscii = 0
    End If
End Sub

Private Sub Form_KeyDown(KeyCode As Integer, Shift As Integer)
    ' منع استخدام مفاتيح معينة
    Select Case KeyCode
        Case vbKeyF11 ' منع التبديل إلى وضع التصميم
            KeyCode = 0
        Case vbKeyF1 ' منع فتح المساعدة
            KeyCode = 0
    End Select
End Sub
