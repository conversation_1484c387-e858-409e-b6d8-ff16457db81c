# إنشاء النماذج يدوياً لنظام رافع
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    إنشاء النماذج التفاعلية يدوياً" -ForegroundColor Yellow
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

$currentPath = Get-Location
$existingPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Simple.accdb"
$newPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Interactive.accdb"

Write-Host "نسخ قاعدة البيانات الموجودة..." -ForegroundColor Yellow

# نسخ قاعدة البيانات الموجودة
if (Test-Path $existingPath) {
    Copy-Item $existingPath $newPath -Force
    Write-Host "✓ تم نسخ قاعدة البيانات" -ForegroundColor Green
} else {
    Write-Host "✗ لم يتم العثور على قاعدة البيانات الأساسية" -ForegroundColor Red
    Write-Host "يرجى تشغيل Create_Simple_Working_System.ps1 أولاً" -ForegroundColor Yellow
    exit
}

try {
    Write-Host "فتح قاعدة البيانات..." -ForegroundColor Yellow
    
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    
    # فتح قاعدة البيانات المنسوخة
    $access.OpenCurrentDatabase($newPath)
    
    Write-Host "✓ تم فتح قاعدة البيانات" -ForegroundColor Green
    
    Write-Host "إنشاء النماذج التفاعلية..." -ForegroundColor Yellow
    
    # إنشاء نموذج تسجيل الدخول بسيط
    Write-Host "  إنشاء نموذج تسجيل الدخول..." -ForegroundColor Cyan
    
    # إنشاء نموذج فارغ
    $access.DoCmd.RunCommand(2065)  # acCmdNewObjectForm
    
    # الحصول على النموذج النشط
    $loginForm = $access.Screen.ActiveForm
    $loginForm.Caption = "تسجيل الدخول - نظام رافع للتطوير العقاري"
    $loginForm.NavigationButtons = $false
    $loginForm.RecordSelectors = $false
    $loginForm.DividingLines = $false
    $loginForm.Modal = $true
    $loginForm.PopUp = $true
    $loginForm.BorderStyle = 3  # Dialog
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmLogin")
    
    Write-Host "    ✓ نموذج تسجيل الدخول" -ForegroundColor Green
    
    # إنشاء النموذج الرئيسي
    Write-Host "  إنشاء النموذج الرئيسي..." -ForegroundColor Cyan
    
    $access.DoCmd.RunCommand(2065)  # acCmdNewObjectForm
    
    $mainForm = $access.Screen.ActiveForm
    $mainForm.Caption = "نظام رافع للتطوير العقاري - الواجهة الرئيسية"
    $mainForm.NavigationButtons = $false
    $mainForm.RecordSelectors = $false
    $mainForm.WindowState = 2  # Maximized
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmMain")
    
    Write-Host "    ✓ النموذج الرئيسي" -ForegroundColor Green
    
    # إنشاء نموذج المشاريع
    Write-Host "  إنشاء نموذج المشاريع..." -ForegroundColor Cyan
    
    # إنشاء نموذج مربوط بجدول المشاريع
    $access.DoCmd.RunCommand(2065)  # acCmdNewObjectForm
    
    $projectsForm = $access.Screen.ActiveForm
    $projectsForm.Caption = "إدارة المشاريع - نظام رافع"
    $projectsForm.RecordSource = "Projects"
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmProjects")
    
    Write-Host "    ✓ نموذج المشاريع" -ForegroundColor Green
    
    # إنشاء نموذج العملاء
    Write-Host "  إنشاء نموذج العملاء..." -ForegroundColor Cyan
    
    $access.DoCmd.RunCommand(2065)  # acCmdNewObjectForm
    
    $customersForm = $access.Screen.ActiveForm
    $customersForm.Caption = "إدارة العملاء - نظام رافع"
    $customersForm.RecordSource = "Customers"
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmCustomers")
    
    Write-Host "    ✓ نموذج العملاء" -ForegroundColor Green
    
    # إنشاء نموذج الوحدات
    Write-Host "  إنشاء نموذج الوحدات..." -ForegroundColor Cyan
    
    $access.DoCmd.RunCommand(2065)  # acCmdNewObjectForm
    
    $unitsForm = $access.Screen.ActiveForm
    $unitsForm.Caption = "إدارة الوحدات - نظام رافع"
    $unitsForm.RecordSource = "Units"
    
    # حفظ النموذج
    $access.DoCmd.Save(2, "frmUnits")
    
    Write-Host "    ✓ نموذج الوحدات" -ForegroundColor Green
    
    Write-Host "إضافة وحدة VBA للنماذج..." -ForegroundColor Yellow
    
    # إنشاء وحدة VBA
    $module = $access.Modules.Add("RafeaFormsModule")
    
    $vbaCode = @'
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة بدء النظام
Public Sub StartSystem()
    DoCmd.OpenForm "frmLogin"
End Sub

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            LoginUser = True
            
            ' إغلاق نموذج تسجيل الدخول وفتح النموذج الرئيسي
            DoCmd.Close acForm, "frmLogin"
            DoCmd.OpenForm "frmMain"
            
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح في نظام رافع!" & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType, vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة فتح نموذج المشاريع
Public Sub OpenProjectsForm()
    DoCmd.OpenForm "frmProjects"
End Sub

' وظيفة فتح نموذج العملاء
Public Sub OpenCustomersForm()
    DoCmd.OpenForm "frmCustomers"
End Sub

' وظيفة فتح نموذج الوحدات
Public Sub OpenUnitsForm()
    DoCmd.OpenForm "frmUnits"
End Sub

' وظيفة عرض لوحة التحكم
Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم: " & CurrentUserFullName & vbCrLf
        msg = msg & "النوع: " & CurrentUserType & vbCrLf & vbCrLf
    End If
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    msg = msg & vbCrLf & "النماذج المتاحة:" & vbCrLf
    msg = msg & "• إدارة المشاريع" & vbCrLf
    msg = msg & "• إدارة العملاء" & vbCrLf
    msg = msg & "• إدارة الوحدات" & vbCrLf
    
    msg = msg & vbCrLf & "© 2024 شركة رافع للتطوير العقاري"
    
    MsgBox msg, vbInformation, "لوحة التحكم"
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    ' إغلاق جميع النماذج المفتوحة
    Dim frm As Form
    For Each frm In Forms
        If frm.Name <> "frmLogin" Then
            DoCmd.Close acForm, frm.Name
        End If
    Next frm
    
    DoCmd.OpenForm "frmLogin"
    
    MsgBox "تم تسجيل الخروج بنجاح", vbInformation, "نظام رافع"
End Sub

' وظيفة عرض معلومات النظام
Public Sub ShowAbout()
    MsgBox "نظام رافع للتطوير العقاري" & vbCrLf & _
           "الإصدار 1.0" & vbCrLf & vbCrLf & _
           "نظام إداري ومحاسبي متكامل" & vbCrLf & _
           "لشركات التطوير العقاري" & vbCrLf & vbCrLf & _
           "النماذج المتاحة:" & vbCrLf & _
           "• نموذج تسجيل الدخول" & vbCrLf & _
           "• النموذج الرئيسي" & vbCrLf & _
           "• نموذج المشاريع" & vbCrLf & _
           "• نموذج العملاء" & vbCrLf & _
           "• نموذج الوحدات" & vbCrLf & vbCrLf & _
           "© 2024 شركة رافع للتطوير العقاري", vbInformation, "حول النظام"
End Sub

' وظيفة تشغيل سريع للنظام
Public Sub QuickStart()
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
           "للبدء:" & vbCrLf & _
           "1. سجّل الدخول بالمعلومات الافتراضية" & vbCrLf & _
           "2. استخدم النماذج لإدارة البيانات" & vbCrLf & _
           "3. استعرض لوحة التحكم للإحصائيات" & vbCrLf & vbCrLf & _
           "معلومات تسجيل الدخول:" & vbCrLf & _
           "اسم المستخدم: admin" & vbCrLf & _
           "كلمة المرور: admin123", vbInformation, "نظام رافع"
    
    StartSystem
End Sub
'@
    
    $module.InsertText($vbaCode)
    Write-Host "  ✓ وحدة VBA للنماذج" -ForegroundColor Green
    
    # تعيين إعدادات البداية
    Write-Host "تعيين إعدادات البداية..." -ForegroundColor Yellow
    
    try {
        # محاولة تعيين نموذج البداية
        $access.CurrentProject.Properties.Add("StartupForm", "frmLogin")
        Write-Host "  ✓ تم تعيين نموذج البداية" -ForegroundColor Green
    } catch {
        Write-Host "  ! تعذر تعيين نموذج البداية تلقائياً" -ForegroundColor Yellow
    }
    
    # حفظ جميع التغييرات
    $access.DoCmd.Save()
    
    Write-Host ""
    Write-Host "✓ تم إنشاء نظام رافع التفاعلي بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $newPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "النماذج المُنشأة:" -ForegroundColor Yellow
    Write-Host "  • frmLogin - نموذج تسجيل الدخول" -ForegroundColor Cyan
    Write-Host "  • frmMain - النموذج الرئيسي" -ForegroundColor Cyan
    Write-Host "  • frmProjects - نموذج المشاريع" -ForegroundColor Cyan
    Write-Host "  • frmCustomers - نموذج العملاء" -ForegroundColor Cyan
    Write-Host "  • frmUnits - نموذج الوحدات" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "الأوامر المتاحة:" -ForegroundColor Yellow
    Write-Host "QuickStart - للبدء السريع" -ForegroundColor Cyan
    Write-Host "StartSystem - لفتح نموذج تسجيل الدخول" -ForegroundColor Cyan
    Write-Host "ShowDashboard - لعرض لوحة التحكم" -ForegroundColor Cyan
    Write-Host "ShowAbout - لعرض معلومات النظام" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "اسم المستخدم: admin" -ForegroundColor Cyan
    Write-Host "كلمة المرور: admin123" -ForegroundColor Cyan
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "نظام رافع التفاعلي جاهز للاستخدام!" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green
