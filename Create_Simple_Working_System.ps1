# إنشاء نظام رافع - النسخة المبسطة العملية
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    إنشاء النسخة المبسطة العملية" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Simple.accdb"

Write-Host "إنشاء نظام رافع المبسط..." -ForegroundColor Yellow
Write-Host "المسار: $systemPath" -ForegroundColor Cyan

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    
    Write-Host "✓ تم فتح Microsoft Access" -ForegroundColor Green
    
    # إنشاء قاعدة بيانات جديدة
    $access.NewCurrentDatabase($systemPath)
    
    Write-Host "✓ تم إنشاء قاعدة البيانات" -ForegroundColor Green
    
    # الحصول على قاعدة البيانات الحالية
    $db = $access.CurrentDb()
    
    Write-Host "إنشاء الجداول..." -ForegroundColor Yellow
    
    # إنشاء جدول المستخدمين
    $sql = "CREATE TABLE Users (UserID COUNTER PRIMARY KEY, Username TEXT(50), Password TEXT(50), FullName TEXT(100), UserType TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء جدول المشاريع
    $sql = "CREATE TABLE Projects (ProjectID COUNTER PRIMARY KEY, ProjectName TEXT(100), ProjectLocation TEXT(100), ProjectType TEXT(50), TotalCost CURRENCY, CompletionPercentage SINGLE, ProjectStatus TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المشاريع" -ForegroundColor Green
    
    # إنشاء جدول العملاء
    $sql = "CREATE TABLE Customers (CustomerID COUNTER PRIMARY KEY, CustomerName TEXT(100), Phone TEXT(20), Mobile TEXT(20), Email TEXT(100), CustomerType TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول العملاء" -ForegroundColor Green
    
    # إنشاء جدول الوحدات
    $sql = "CREATE TABLE Units (UnitID COUNTER PRIMARY KEY, ProjectID LONG, UnitNumber TEXT(20), UnitType TEXT(50), Area SINGLE, UnitPrice CURRENCY, UnitStatus TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الوحدات" -ForegroundColor Green
    
    Write-Host "إدراج البيانات..." -ForegroundColor Yellow
    
    # إدراج المستخدمين
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('accountant', '123456', 'المحاسب', 'محاسب');"
    $db.Execute($sql)
    
    Write-Host "  ✓ المستخدمين" -ForegroundColor Green
    
    # إدراج المشاريع
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('مشروع الواحة السكني', 'الرياض', 'سكني', 5000000, 75, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('برج التجارة', 'جدة', 'تجاري', ********, 45, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    Write-Host "  ✓ المشاريع" -ForegroundColor Green
    
    # إدراج العملاء
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType) VALUES ('أحمد محمد السعيد', '011-4567890', '**********', '<EMAIL>', 'فرد');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType) VALUES ('شركة الخليج للاستثمار', '012-9876543', '0559876543', '<EMAIL>', 'شركة');"
    $db.Execute($sql)
    
    Write-Host "  ✓ العملاء" -ForegroundColor Green
    
    # إدراج الوحدات
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-101', 'شقة', 120, 450000, 'متاح');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-102', 'شقة', 140, 520000, 'محجوز');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (2, 'OF-301', 'مكتب', 80, 320000, 'متاح');"
    $db.Execute($sql)
    
    Write-Host "  ✓ الوحدات" -ForegroundColor Green
    
    Write-Host "إنشاء وحدة VBA..." -ForegroundColor Yellow
    
    # إنشاء وحدة VBA
    $module = $access.Modules.Add("RafeaSystem")
    
    $vbaCode = @'
Option Compare Database
Option Explicit

Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        CurrentUserID = rs!UserID
        CurrentUserName = rs!Username
        CurrentUserFullName = rs!FullName
        CurrentUserType = rs!UserType
        
        LoginUser = True
        
        MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
               "تم تسجيل الدخول بنجاح في نظام رافع!" & vbCrLf & _
               "نوع المستخدم: " & CurrentUserType, vbInformation, "نظام رافع"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم: " & CurrentUserFullName & vbCrLf
        msg = msg & "النوع: " & CurrentUserType & vbCrLf & vbCrLf
    End If
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close
    
    msg = msg & vbCrLf & "وحدات النظام:" & vbCrLf
    msg = msg & "✓ إدارة المشاريع" & vbCrLf
    msg = msg & "✓ إدارة العملاء" & vbCrLf
    msg = msg & "✓ إدارة الوحدات" & vbCrLf
    msg = msg & "✓ التقارير" & vbCrLf
    
    msg = msg & vbCrLf & "© 2024 شركة رافع للتطوير العقاري"
    
    MsgBox msg, vbInformation, "لوحة التحكم"
    
    Set rs = Nothing
    Set db = Nothing
End Sub

Public Sub ShowProjects()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Projects")
    
    msg = "═══ قائمة المشاريع ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!ProjectName & vbCrLf
        msg = msg & "  الموقع: " & rs!ProjectLocation & vbCrLf
        msg = msg & "  النوع: " & rs!ProjectType & vbCrLf
        msg = msg & "  التكلفة: " & Format(rs!TotalCost, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  نسبة الإنجاز: " & rs!CompletionPercentage & "%" & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "المشاريع"
End Sub

Public Sub ShowCustomers()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Customers")
    
    msg = "═══ قائمة العملاء ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!CustomerName & vbCrLf
        msg = msg & "  النوع: " & rs!CustomerType & vbCrLf
        msg = msg & "  الجوال: " & rs!Mobile & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "العملاء"
End Sub

Public Sub ShowUnits()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT u.*, p.ProjectName FROM Units u LEFT JOIN Projects p ON u.ProjectID = p.ProjectID")
    
    msg = "═══ قائمة الوحدات ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• وحدة " & rs!UnitNumber
        If Not IsNull(rs!ProjectName) Then msg = msg & " - " & rs!ProjectName
        msg = msg & vbCrLf
        msg = msg & "  النوع: " & rs!UnitType & vbCrLf
        msg = msg & "  المساحة: " & rs!Area & " م²" & vbCrLf
        msg = msg & "  السعر: " & Format(rs!UnitPrice, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  الحالة: " & rs!UnitStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "الوحدات"
End Sub

Public Sub ShowSystemInfo()
    MsgBox "════════════════════════════════════════" & vbCrLf & _
           "    نظام رافع للتطوير العقاري" & vbCrLf & _
           "    الإصدار 1.0" & vbCrLf & _
           "════════════════════════════════════════" & vbCrLf & vbCrLf & _
           "نظام إداري ومحاسبي متكامل" & vbCrLf & _
           "لشركات التطوير العقاري" & vbCrLf & vbCrLf & _
           "الميزات:" & vbCrLf & _
           "✓ إدارة المشاريع العقارية" & vbCrLf & _
           "✓ إدارة العملاء والمبيعات" & vbCrLf & _
           "✓ إدارة الوحدات السكنية" & vbCrLf & _
           "✓ نظام التقارير" & vbCrLf & _
           "✓ نظام الصلاحيات" & vbCrLf & vbCrLf & _
           "© 2024 شركة رافع للتطوير العقاري", vbInformation, "معلومات النظام"
End Sub
'@
    
    $module.InsertText($vbaCode)
    Write-Host "  ✓ وحدة VBA" -ForegroundColor Green
    
    # حفظ جميع التغييرات
    $access.DoCmd.Save()
    
    Write-Host ""
    Write-Host "✓ تم إنشاء نظام رافع بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $systemPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "المدير: admin / admin123" -ForegroundColor Cyan
    Write-Host "المحاسب: accountant / 123456" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "الأوامر المتاحة (اضغط Ctrl+G):" -ForegroundColor Yellow
    Write-Host "LoginUser(""admin"", ""admin123"")" -ForegroundColor Cyan
    Write-Host "ShowDashboard" -ForegroundColor Cyan
    Write-Host "ShowProjects" -ForegroundColor Cyan
    Write-Host "ShowCustomers" -ForegroundColor Cyan
    Write-Host "ShowUnits" -ForegroundColor Cyan
    Write-Host "ShowSystemInfo" -ForegroundColor Cyan
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "نظام رافع للتطوير العقاري جاهز!" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green
