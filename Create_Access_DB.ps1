# إنشاء قاعدة بيانات Access لنظام رافع
Write-Host "========================================" -ForegroundColor Green
Write-Host "    إنشاء قاعدة بيانات نظام رافع" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$currentPath = Get-Location
$databasePath = Join-Path $currentPath "RafeaSystem\Database\RafeaSystem_BE.accdb"
$frontendPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_FE.accdb"

Write-Host "المسار الحالي: $currentPath" -ForegroundColor Cyan
Write-Host "مسار قاعدة البيانات: $databasePath" -ForegroundColor Cyan

try {
    Write-Host "إنشاء قاعدة البيانات..." -ForegroundColor Yellow
    
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # إنشاء قاعدة البيانات الخلفية
    $db = $access.DBEngine.CreateDatabase($databasePath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "✓ تم إنشاء قاعدة البيانات الأساسية" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين
    $sql = @"
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FullName TEXT(100) NOT NULL,
    UserType TEXT(20) NOT NULL DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء جدول المشاريع
    $sql = @"
CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(200) NOT NULL,
    ProjectLocation TEXT(300),
    ProjectType TEXT(50) DEFAULT 'سكني',
    TotalCost CURRENCY DEFAULT 0,
    StartDate DATETIME,
    ExpectedEndDate DATETIME,
    ActualEndDate DATETIME,
    CompletionPercentage SINGLE DEFAULT 0,
    ProjectStatus TEXT(20) DEFAULT 'قيد التنفيذ',
    Description MEMO,
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول المشاريع" -ForegroundColor Green
    
    # إنشاء جدول العملاء
    $sql = @"
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerName TEXT(100) NOT NULL,
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    NationalID TEXT(20),
    CustomerType TEXT(20) DEFAULT 'فرد',
    Notes MEMO,
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول العملاء" -ForegroundColor Green
    
    # إنشاء جدول الوحدات
    $sql = @"
CREATE TABLE Units (
    UnitID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    UnitNumber TEXT(20) NOT NULL,
    UnitType TEXT(50) DEFAULT 'شقة',
    Area SINGLE DEFAULT 0,
    Floor INTEGER DEFAULT 0,
    Rooms INTEGER DEFAULT 0,
    Bathrooms INTEGER DEFAULT 0,
    UnitPrice CURRENCY DEFAULT 0,
    UnitStatus TEXT(20) DEFAULT 'متاح'
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول الوحدات" -ForegroundColor Green
    
    # إدراج المستخدم الافتراضي
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
    $db.Execute($sql)
    Write-Host "✓ المستخدم الافتراضي" -ForegroundColor Green
    
    # إدراج بيانات تجريبية
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('مشروع الواحة السكني', 'الرياض - حي النرجس', 'سكني', 5000000, 75, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('برج التجارة', 'جدة - الكورنيش', 'تجاري', 12000000, 45, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    Write-Host "✓ بيانات تجريبية" -ForegroundColor Green
    
    $db.Close()
    
    # إنشاء الواجهة الأمامية
    Write-Host "إنشاء الواجهة الأمامية..." -ForegroundColor Yellow
    
    $frontendDB = $access.DBEngine.CreateDatabase($frontendPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    # ربط الجداول
    $linkedTable = $frontendDB.CreateTableDef("Users")
    $linkedTable.Connect = ";DATABASE=$databasePath"
    $linkedTable.SourceTableName = "Users"
    $frontendDB.TableDefs.Append($linkedTable)
    
    $linkedTable = $frontendDB.CreateTableDef("Projects")
    $linkedTable.Connect = ";DATABASE=$databasePath"
    $linkedTable.SourceTableName = "Projects"
    $frontendDB.TableDefs.Append($linkedTable)
    
    $linkedTable = $frontendDB.CreateTableDef("Customers")
    $linkedTable.Connect = ";DATABASE=$databasePath"
    $linkedTable.SourceTableName = "Customers"
    $frontendDB.TableDefs.Append($linkedTable)
    
    $linkedTable = $frontendDB.CreateTableDef("Units")
    $linkedTable.Connect = ";DATABASE=$databasePath"
    $linkedTable.SourceTableName = "Units"
    $frontendDB.TableDefs.Append($linkedTable)
    
    Write-Host "✓ ربط الجداول" -ForegroundColor Green
    
    $frontendDB.Close()
    
    # إنشاء وحدة VBA أساسية
    $access.OpenCurrentDatabase($frontendPath)
    
    $module = $access.Modules.Add("CoreFunctions")
    $vbaCode = @'
Option Compare Database
Option Explicit

Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            db.Execute "UPDATE Users SET LastLogin = Now() WHERE UserID = " & CurrentUserID
            
            LoginUser = True
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & "تم تسجيل الدخول بنجاح!", vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ في تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    
    msg = "=== لوحة تحكم نظام رافع ===" & vbCrLf & vbCrLf
    msg = msg & "المستخدم: " & CurrentUserFullName & vbCrLf
    msg = msg & "النوع: " & CurrentUserType & vbCrLf
    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & vbCrLf
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects WHERE ProjectStatus = 'قيد التنفيذ'")
    msg = msg & "المشاريع النشطة: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    msg = msg & vbCrLf & "=== وحدات النظام ===" & vbCrLf
    msg = msg & "• إدارة المشاريع" & vbCrLf
    msg = msg & "• إدارة المبيعات" & vbCrLf
    msg = msg & "• إدارة المقاولين" & vbCrLf
    msg = msg & "• إدارة الموردين" & vbCrLf
    msg = msg & "• إدارة المشتريات" & vbCrLf
    msg = msg & "• الصيانة والتشغيل" & vbCrLf
    msg = msg & "• المهام اليومية" & vbCrLf
    msg = msg & "• التقارير" & vbCrLf
    
    MsgBox msg, vbInformation, "نظام رافع للتطوير العقاري"
    
    Set rs = Nothing
    Set db = Nothing
End Sub
'@
    
    $module.InsertText($vbaCode)
    Write-Host "✓ وحدة VBA الأساسية" -ForegroundColor Green
    
    $access.DoCmd.Save()
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host ""
    Write-Host "✓ تم إنشاء النظام بنجاح!" -ForegroundColor Green
    Write-Host "قاعدة البيانات: $databasePath" -ForegroundColor Cyan
    Write-Host "الواجهة الأمامية: $frontendPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "اسم المستخدم: admin" -ForegroundColor Cyan
    Write-Host "كلمة المرور: admin123" -ForegroundColor Cyan
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط Enter لتشغيل النظام..." -ForegroundColor Green
Read-Host

# تشغيل النظام
if (Test-Path $frontendPath) {
    Write-Host "تشغيل نظام رافع..." -ForegroundColor Green
    Start-Process $frontendPath
} else {
    Write-Host "لم يتم العثور على ملف النظام" -ForegroundColor Red
}
