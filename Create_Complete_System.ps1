# إنشاء نظام رافع للتطوير العقاري - النسخة الكاملة
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    إنشاء النظام الكامل" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Complete.accdb"

Write-Host "إنشاء نظام رافع الكامل..." -ForegroundColor Yellow
Write-Host "المسار: $systemPath" -ForegroundColor Cyan

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true

    # إنشاء قاعدة البيانات
    $db = $access.DBEngine.CreateDatabase($systemPath, "")

    Write-Host "✓ تم إنشاء قاعدة البيانات الأساسية" -ForegroundColor Green

    # إنشاء جدول المستخدمين
    Write-Host "إنشاء الجداول..." -ForegroundColor Yellow

    $sql = "CREATE TABLE Users (" +
           "UserID COUNTER PRIMARY KEY, " +
           "Username TEXT(50) NOT NULL, " +
           "Password TEXT(255) NOT NULL, " +
           "FullName TEXT(100) NOT NULL, " +
           "UserType TEXT(20) DEFAULT 'مستخدم', " +
           "IsActive YESNO DEFAULT Yes, " +
           "CreatedDate DATETIME DEFAULT Now(), " +
           "LastLogin DATETIME" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخدمين" -ForegroundColor Green

    # إنشاء جدول المشاريع
    $sql = "CREATE TABLE Projects (" +
           "ProjectID COUNTER PRIMARY KEY, " +
           "ProjectName TEXT(200) NOT NULL, " +
           "ProjectLocation TEXT(300), " +
           "ProjectType TEXT(50) DEFAULT 'سكني', " +
           "TotalCost CURRENCY DEFAULT 0, " +
           "StartDate DATETIME, " +
           "ExpectedEndDate DATETIME, " +
           "ActualEndDate DATETIME, " +
           "CompletionPercentage SINGLE DEFAULT 0, " +
           "ProjectStatus TEXT(20) DEFAULT 'قيد التنفيذ', " +
           "Description MEMO, " +
           "CreatedBy LONG, " +
           "CreatedDate DATETIME DEFAULT Now()" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المشاريع" -ForegroundColor Green

    # إنشاء جدول العملاء
    $sql = "CREATE TABLE Customers (" +
           "CustomerID COUNTER PRIMARY KEY, " +
           "CustomerName TEXT(100) NOT NULL, " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "Email TEXT(100), " +
           "Address TEXT(300), " +
           "NationalID TEXT(20), " +
           "CustomerType TEXT(20) DEFAULT 'فرد', " +
           "Notes MEMO, " +
           "CreatedDate DATETIME DEFAULT Now()" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول العملاء" -ForegroundColor Green

    # إنشاء جدول الوحدات
    $sql = "CREATE TABLE Units (" +
           "UnitID COUNTER PRIMARY KEY, " +
           "ProjectID LONG NOT NULL, " +
           "UnitNumber TEXT(20) NOT NULL, " +
           "UnitType TEXT(50) DEFAULT 'شقة', " +
           "Area SINGLE DEFAULT 0, " +
           "Floor INTEGER DEFAULT 0, " +
           "Rooms INTEGER DEFAULT 0, " +
           "Bathrooms INTEGER DEFAULT 0, " +
           "UnitPrice CURRENCY DEFAULT 0, " +
           "UnitStatus TEXT(20) DEFAULT 'متاح'" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الوحدات" -ForegroundColor Green

    # إنشاء جدول العقود
    $sql = "CREATE TABLE Contracts (" +
           "ContractID COUNTER PRIMARY KEY, " +
           "CustomerID LONG NOT NULL, " +
           "UnitID LONG NOT NULL, " +
           "ContractDate DATETIME DEFAULT Now(), " +
           "TotalAmount CURRENCY DEFAULT 0, " +
           "DownPayment CURRENCY DEFAULT 0, " +
           "MonthlyInstallment CURRENCY DEFAULT 0, " +
           "InstallmentCount INTEGER DEFAULT 0, " +
           "ContractStatus TEXT(20) DEFAULT 'نشط', " +
           "Notes MEMO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول العقود" -ForegroundColor Green

    # إنشاء جدول المدفوعات
    $sql = "CREATE TABLE Payments (" +
           "PaymentID COUNTER PRIMARY KEY, " +
           "ContractID LONG NOT NULL, " +
           "PaymentDate DATETIME DEFAULT Now(), " +
           "PaymentAmount CURRENCY DEFAULT 0, " +
           "PaymentMethod TEXT(50) DEFAULT 'نقدي', " +
           "CheckNumber TEXT(50), " +
           "BankName TEXT(100), " +
           "PaymentStatus TEXT(20) DEFAULT 'مدفوع', " +
           "Notes MEMO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المدفوعات" -ForegroundColor Green

    # إنشاء جدول المقاولين
    $sql = "CREATE TABLE Contractors (" +
           "ContractorID COUNTER PRIMARY KEY, " +
           "ContractorName TEXT(100) NOT NULL, " +
           "CompanyName TEXT(150), " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "Email TEXT(100), " +
           "Address TEXT(300), " +
           "TaxNumber TEXT(50), " +
           "Specialization TEXT(100), " +
           "IsActive YESNO DEFAULT Yes, " +
           "CreatedDate DATETIME DEFAULT Now()" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المقاولين" -ForegroundColor Green

    # إنشاء جدول عقود المقاولين
    $sql = "CREATE TABLE ContractorContracts (" +
           "ContractorContractID COUNTER PRIMARY KEY, " +
           "ContractorID LONG NOT NULL, " +
           "ProjectID LONG NOT NULL, " +
           "ContractDate DATETIME DEFAULT Now(), " +
           "ContractAmount CURRENCY DEFAULT 0, " +
           "WorkDescription MEMO, " +
           "StartDate DATETIME, " +
           "EndDate DATETIME, " +
           "ContractStatus TEXT(20) DEFAULT 'نشط'" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول عقود المقاولين" -ForegroundColor Green

    # إنشاء جدول المستخلصات
    $sql = "CREATE TABLE Extracts (" +
           "ExtractID COUNTER PRIMARY KEY, " +
           "ContractorContractID LONG NOT NULL, " +
           "ExtractNumber TEXT(50), " +
           "ExtractDate DATETIME DEFAULT Now(), " +
           "ExtractAmount CURRENCY DEFAULT 0, " +
           "WorkDescription MEMO, " +
           "CompletionPercentage SINGLE DEFAULT 0, " +
           "ExtractStatus TEXT(20) DEFAULT 'معلق', " +
           "ApprovedBy LONG, " +
           "ApprovalDate DATETIME" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخلصات" -ForegroundColor Green

    # إنشاء جدول الموردين
    $sql = "CREATE TABLE Suppliers (" +
           "SupplierID COUNTER PRIMARY KEY, " +
           "SupplierName TEXT(100) NOT NULL, " +
           "CompanyName TEXT(150), " +
           "Phone TEXT(20), " +
           "Mobile TEXT(20), " +
           "Email TEXT(100), " +
           "Address TEXT(300), " +
           "TaxNumber TEXT(50), " +
           "SupplierType TEXT(50), " +
           "IsActive YESNO DEFAULT Yes, " +
           "CreatedDate DATETIME DEFAULT Now()" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الموردين" -ForegroundColor Green

    # إنشاء جدول الفواتير
    $sql = "CREATE TABLE Invoices (" +
           "InvoiceID COUNTER PRIMARY KEY, " +
           "SupplierID LONG NOT NULL, " +
           "ProjectID LONG, " +
           "InvoiceNumber TEXT(50), " +
           "InvoiceDate DATETIME DEFAULT Now(), " +
           "TotalAmount CURRENCY DEFAULT 0, " +
           "TaxAmount CURRENCY DEFAULT 0, " +
           "NetAmount CURRENCY DEFAULT 0, " +
           "InvoiceStatus TEXT(20) DEFAULT 'معلق', " +
           "DueDate DATETIME, " +
           "Notes MEMO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الفواتير" -ForegroundColor Green

    # إنشاء جدول طلبات الشراء
    $sql = "CREATE TABLE PurchaseOrders (" +
           "PurchaseOrderID COUNTER PRIMARY KEY, " +
           "ProjectID LONG, " +
           "SupplierID LONG, " +
           "OrderNumber TEXT(50), " +
           "OrderDate DATETIME DEFAULT Now(), " +
           "TotalAmount CURRENCY DEFAULT 0, " +
           "OrderStatus TEXT(20) DEFAULT 'معلق', " +
           "RequestedBy LONG, " +
           "ApprovedBy LONG, " +
           "ApprovalDate DATETIME, " +
           "DeliveryDate DATETIME, " +
           "Notes MEMO" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول طلبات الشراء" -ForegroundColor Green

    # إنشاء جدول الصيانة
    $sql = "CREATE TABLE Maintenance (" +
           "MaintenanceID COUNTER PRIMARY KEY, " +
           "ProjectID LONG, " +
           "UnitID LONG, " +
           "MaintenanceType TEXT(50), " +
           "Description MEMO, " +
           "RequestDate DATETIME DEFAULT Now(), " +
           "ScheduledDate DATETIME, " +
           "CompletedDate DATETIME, " +
           "MaintenanceCost CURRENCY DEFAULT 0, " +
           "MaintenanceStatus TEXT(20) DEFAULT 'مطلوب', " +
           "AssignedTo LONG, " +
           "Priority TEXT(20) DEFAULT 'عادي'" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الصيانة" -ForegroundColor Green

    # إنشاء جدول المهام
    $sql = "CREATE TABLE Tasks (" +
           "TaskID COUNTER PRIMARY KEY, " +
           "TaskTitle TEXT(200) NOT NULL, " +
           "TaskDescription MEMO, " +
           "AssignedTo LONG, " +
           "AssignedBy LONG, " +
           "ProjectID LONG, " +
           "TaskDate DATETIME DEFAULT Now(), " +
           "DueDate DATETIME, " +
           "CompletedDate DATETIME, " +
           "TaskStatus TEXT(20) DEFAULT 'جديد', " +
           "Priority TEXT(20) DEFAULT 'عادي', " +
           "TaskType TEXT(50)" +
           ");"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المهام" -ForegroundColor Green

    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Yellow

    # إدراج المستخدمين
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('accountant', '123456', 'المحاسب الرئيسي', 'محاسب');"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('engineer', '123456', 'المهندس المشرف', 'مهندس');"
    $db.Execute($sql)

    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('sales', '123456', 'موظف المبيعات', 'مبيعات');"
    $db.Execute($sql)

    Write-Host "  ✓ المستخدمين الأساسيين" -ForegroundColor Green

    # إدراج مشاريع تجريبية
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedBy) VALUES ('مشروع الواحة السكني', 'الرياض - حي النرجس', 'سكني', 5000000, 75, 'قيد التنفيذ', 'مشروع سكني متكامل يضم 120 وحدة سكنية متنوعة', 1);"
    $db.Execute($sql)

    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedBy) VALUES ('برج التجارة المركزي', 'جدة - الكورنيش', 'تجاري', 12000000, 45, 'قيد التنفيذ', 'برج تجاري يضم مكاتب ومحلات تجارية على 25 طابق', 1);"
    $db.Execute($sql)

    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description, CreatedBy) VALUES ('مجمع الأعمال الذكي', 'الدمام - الخبر', 'إداري', 8000000, 90, 'قيد التنفيذ', 'مجمع إداري حديث بتقنيات ذكية ومساحات عمل مرنة', 1);"
    $db.Execute($sql)

    Write-Host "  ✓ المشاريع التجريبية" -ForegroundColor Green

    # إدراج عملاء تجريبيين
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address) VALUES ('أحمد محمد السعيد', '011-4567890', '0501234567', '<EMAIL>', 'فرد', 'الرياض - حي الملك فهد');"
    $db.Execute($sql)

    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address) VALUES ('شركة الخليج للاستثمار', '012-9876543', '0559876543', '<EMAIL>', 'شركة', 'جدة - شارع التحلية');"
    $db.Execute($sql)

    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address) VALUES ('فاطمة علي الزهراني', '013-5555555', '0505555555', '<EMAIL>', 'فرد', 'الدمام - حي الشاطئ');"
    $db.Execute($sql)

    Write-Host "  ✓ العملاء التجريبيين" -ForegroundColor Green

    # إدراج وحدات تجريبية
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, Floor, Rooms, Bathrooms, UnitPrice, UnitStatus) VALUES (1, 'A-101', 'شقة', 120, 1, 3, 2, 450000, 'متاح');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, Floor, Rooms, Bathrooms, UnitPrice, UnitStatus) VALUES (1, 'A-102', 'شقة', 140, 1, 4, 3, 520000, 'محجوز');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, Floor, Rooms, Bathrooms, UnitPrice, UnitStatus) VALUES (1, 'B-201', 'شقة', 160, 2, 4, 3, 580000, 'متاح');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, Floor, Rooms, Bathrooms, UnitPrice, UnitStatus) VALUES (2, 'OF-301', 'مكتب', 80, 3, 0, 1, 320000, 'متاح');"
    $db.Execute($sql)

    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, Floor, Rooms, Bathrooms, UnitPrice, UnitStatus) VALUES (2, 'SH-101', 'محل', 60, 1, 0, 1, 280000, 'مباع');"
    $db.Execute($sql)

    Write-Host "  ✓ الوحدات التجريبية" -ForegroundColor Green

    # إدراج مقاولين تجريبيين
    $sql = "INSERT INTO Contractors (ContractorName, CompanyName, Phone, Mobile, Email, Specialization) VALUES ('محمد أحمد البناء', 'شركة البناء المتقدم', '011-7777777', '0507777777', '<EMAIL>', 'أعمال الخرسانة والحديد');"
    $db.Execute($sql)

    $sql = "INSERT INTO Contractors (ContractorName, CompanyName, Phone, Mobile, Email, Specialization) VALUES ('سعد علي الكهرباء', 'مؤسسة الكهرباء الحديثة', '012-8888888', '0508888888', '<EMAIL>', 'الأعمال الكهربائية');"
    $db.Execute($sql)

    Write-Host "  ✓ المقاولين التجريبيين" -ForegroundColor Green

    # إدراج موردين تجريبيين
    $sql = "INSERT INTO Suppliers (SupplierName, CompanyName, Phone, Mobile, Email, SupplierType) VALUES ('خالد محمد التوريد', 'شركة مواد البناء المتحدة', '011-9999999', '0509999999', '<EMAIL>', 'مواد البناء');"
    $db.Execute($sql)

    $sql = "INSERT INTO Suppliers (SupplierName, CompanyName, Phone, Mobile, Email, SupplierType) VALUES ('أحمد سالم الحديد', 'مصنع الحديد الوطني', '013-1111111', '0501111111', '<EMAIL>', 'حديد وصلب');"
    $db.Execute($sql)

    Write-Host "  ✓ الموردين التجريبيين" -ForegroundColor Green

    $db.Close()

    Write-Host "إنشاء وحدات VBA..." -ForegroundColor Yellow

    # فتح قاعدة البيانات لإضافة VBA
    $access.OpenCurrentDatabase($systemPath)

    # إنشاء وحدة النظام الأساسية
    $module = $access.Modules.Add("RafeaSystemCore")

    $vbaCode = @'
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String

    On Error GoTo ErrorHandler

    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)

    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType

            ' تحديث آخر تسجيل دخول
            db.Execute "UPDATE Users SET LastLogin = Now() WHERE UserID = " & CurrentUserID

            LoginUser = True

            ' عرض رسالة ترحيب
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType & vbCrLf & _
                   "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & _
                   "الوقت: " & Format(Time, "hh:nn AM/PM"), vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط. يرجى الاتصال بالمدير.", vbExclamation, "خطأ في تسجيل الدخول"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة.", vbExclamation, "خطأ في تسجيل الدخول"
        LoginUser = False
    End If

    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function

ErrorHandler:
    MsgBox "حدث خطأ أثناء تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function
'@

    $module.InsertText($vbaCode)
    Write-Host "  ✓ وحدة النظام الأساسية" -ForegroundColor Green

    # إنشاء وحدة لوحة التحكم
    $dashboardModule = $access.Modules.Add("DashboardModule")

    $dashboardCode = @'
Option Compare Database
Option Explicit

' وظيفة عرض لوحة التحكم الرئيسية
Public Sub ShowMainDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String

    On Error GoTo ErrorHandler

    Set db = CurrentDb

    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم الرئيسية" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf

    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم الحالي: " & CurrentUserFullName & vbCrLf
        msg = msg & "نوع المستخدم: " & CurrentUserType & vbCrLf
    End If

    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf
    msg = msg & "الوقت: " & Format(Time, "hh:nn AM/PM") & vbCrLf & vbCrLf

    msg = msg & "═══ إحصائيات النظام ═══" & vbCrLf

    ' إحصائيات المشاريع
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects WHERE ProjectStatus = 'قيد التنفيذ'")
    msg = msg & "المشاريع النشطة: " & rs!Total & vbCrLf
    rs.Close

    ' إحصائيات العملاء
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close

    ' إحصائيات الوحدات
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'متاح'")
    msg = msg & "الوحدات المتاحة: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'محجوز'")
    msg = msg & "الوحدات المحجوزة: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'مباع'")
    msg = msg & "الوحدات المباعة: " & rs!Total & vbCrLf
    rs.Close

    ' إحصائيات المقاولين والموردين
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Contractors WHERE IsActive = True")
    msg = msg & "المقاولين النشطين: " & rs!Total & vbCrLf
    rs.Close

    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Suppliers WHERE IsActive = True")
    msg = msg & "الموردين النشطين: " & rs!Total & vbCrLf
    rs.Close

    ' إجمالي قيمة المشاريع
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close

    msg = msg & vbCrLf & "═══ وحدات النظام المتاحة ═══" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات السكنية" & vbCrLf
    msg = msg & "✓ إدارة العملاء والعقود والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ إدارة المشتريات وطلبات الشراء" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتكاليف التشغيلية" & vbCrLf
    msg = msg & "✓ إدارة المهام اليومية" & vbCrLf
    msg = msg & "✓ نظام التقارير الشامل" & vbCrLf
    msg = msg & "✓ نظام الصلاحيات المتقدم" & vbCrLf
    msg = msg & "✓ النسخ الاحتياطي التلقائي" & vbCrLf

    msg = msg & vbCrLf & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"

    MsgBox msg, vbInformation, "لوحة التحكم - نظام رافع"

    Set rs = Nothing
    Set db = Nothing
    Exit Sub

ErrorHandler:
    MsgBox "خطأ في عرض لوحة التحكم: " & Err.Description, vbCritical, "خطأ"
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub
'@

    $dashboardModule.InsertText($dashboardCode)
    Write-Host "  ✓ وحدة لوحة التحكم" -ForegroundColor Green

    # حفظ جميع التغييرات
    $access.DoCmd.Save()

    Write-Host ""
    Write-Host "✓ تم إنشاء نظام رافع الكامل بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $systemPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "المدير - اسم المستخدم: admin | كلمة المرور: admin123" -ForegroundColor Cyan
    Write-Host "المحاسب - اسم المستخدم: accountant | كلمة المرور: 123456" -ForegroundColor Cyan
    Write-Host "المهندس - اسم المستخدم: engineer | كلمة المرور: 123456" -ForegroundColor Cyan
    Write-Host "المبيعات - اسم المستخدم: sales | كلمة المرور: 123456" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "الأوامر المتاحة في نافذة Immediate (Ctrl+G):" -ForegroundColor Yellow
    Write-Host "LoginUser(""admin"", ""admin123"")" -ForegroundColor Cyan
    Write-Host "ShowMainDashboard" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "النظام جاهز للاستخدام!" -ForegroundColor Green

}
catch {
    Write-Host "خطأ في إنشاء النظام: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "تفاصيل الخطأ: $($_.Exception.InnerException)" -ForegroundColor Red
}

Write-Host ""
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "انتهى إنشاء نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green