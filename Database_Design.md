# تصميم قاعدة البيانات - نظام رافع للتطوير العقاري

## 1. الجداول الأساسية

### جدول المستخدمين (Users)
- UserID (Primary Key, AutoNumber)
- Username (Text, 50)
- Password (Text, 255) - مشفر
- FullName (Text, 100)
- UserType (Text, 20) - مدير، محاسب، مهندس، مبيعات
- IsActive (Yes/No)
- CreatedDate (Date/Time)
- LastLogin (Date/Time)

### جدول الصلاحيات (Permissions)
- PermissionID (Primary Key, AutoNumber)
- UserID (Foreign Key)
- ModuleName (Text, 50)
- CanView (Yes/No)
- CanAdd (Yes/No)
- CanEdit (Yes/No)
- CanDelete (Yes/No)
- CanPrint (Yes/No)

## 2. جداول إدارة المشاريع

### جدول المشاريع (Projects)
- ProjectID (Primary Key, AutoNumber)
- ProjectName (Text, 200)
- ProjectLocation (Text, 300)
- ProjectType (Text, 50) - سكني، تجاري، إداري
- TotalCost (Currency)
- StartDate (Date/Time)
- ExpectedEndDate (Date/Time)
- ActualEndDate (Date/Time)
- CompletionPercentage (Number, Single)
- ProjectStatus (Text, 20) - قيد التنفيذ، مكتمل، متوقف
- Description (Memo)
- CreatedBy (Foreign Key - Users)
- CreatedDate (Date/Time)

### جدول الوحدات السكنية (Units)
- UnitID (Primary Key, AutoNumber)
- ProjectID (Foreign Key)
- UnitNumber (Text, 20)
- UnitType (Text, 50) - شقة، فيلا، محل
- Area (Number, Single)
- Floor (Number, Integer)
- Rooms (Number, Integer)
- Bathrooms (Number, Integer)
- UnitPrice (Currency)
- UnitStatus (Text, 20) - متاح، محجوز، مباع، تحت الصيانة

## 3. جداول المبيعات

### جدول العملاء (Customers)
- CustomerID (Primary Key, AutoNumber)
- CustomerName (Text, 100)
- Phone (Text, 20)
- Mobile (Text, 20)
- Email (Text, 100)
- Address (Text, 300)
- NationalID (Text, 20)
- CustomerType (Text, 20) - فرد، شركة
- Notes (Memo)
- CreatedDate (Date/Time)

### جدول العقود (Contracts)
- ContractID (Primary Key, AutoNumber)
- CustomerID (Foreign Key)
- UnitID (Foreign Key)
- ContractDate (Date/Time)
- TotalAmount (Currency)
- DownPayment (Currency)
- InstallmentAmount (Currency)
- NumberOfInstallments (Number, Integer)
- ContractStatus (Text, 20) - نشط، مكتمل، ملغي
- Notes (Memo)
- CreatedBy (Foreign Key - Users)

### جدول المدفوعات (Payments)
- PaymentID (Primary Key, AutoNumber)
- ContractID (Foreign Key)
- PaymentDate (Date/Time)
- PaymentAmount (Currency)
- PaymentMethod (Text, 20) - نقدي، شيك، تحويل
- PaymentStatus (Text, 20) - مدفوع، معلق، مرتد
- CheckNumber (Text, 50)
- BankName (Text, 100)
- Notes (Memo)
- ReceivedBy (Foreign Key - Users)

## 4. جداول المقاولين

### جدول المقاولين (Contractors)
- ContractorID (Primary Key, AutoNumber)
- ContractorName (Text, 100)
- CompanyName (Text, 150)
- Phone (Text, 20)
- Mobile (Text, 20)
- Email (Text, 100)
- Address (Text, 300)
- TaxNumber (Text, 20)
- Specialization (Text, 100)
- IsActive (Yes/No)

### جدول عقود المقاولين (ContractorContracts)
- ContractorContractID (Primary Key, AutoNumber)
- ProjectID (Foreign Key)
- ContractorID (Foreign Key)
- ContractDate (Date/Time)
- ContractAmount (Currency)
- WorkDescription (Memo)
- StartDate (Date/Time)
- EndDate (Date/Time)
- ContractStatus (Text, 20)

### جدول المستخلصات (Extracts)
- ExtractID (Primary Key, AutoNumber)
- ContractorContractID (Foreign Key)
- ExtractNumber (Text, 20)
- ExtractDate (Date/Time)
- ExtractAmount (Currency)
- WorkPercentage (Number, Single)
- ExtractStatus (Text, 20) - معتمد، قيد المراجعة، مرفوض
- ApprovedBy (Foreign Key - Users)
- ApprovalDate (Date/Time)
- Notes (Memo)

### جدول مدفوعات المقاولين (ContractorPayments)
- PaymentID (Primary Key, AutoNumber)
- ExtractID (Foreign Key)
- PaymentDate (Date/Time)
- PaymentAmount (Currency)
- PaymentMethod (Text, 20)
- CheckNumber (Text, 50)
- BankName (Text, 100)
- PaidBy (Foreign Key - Users)
- Notes (Memo)

## 5. جداول الموردين

### جدول الموردين (Suppliers)
- SupplierID (Primary Key, AutoNumber)
- SupplierName (Text, 100)
- CompanyName (Text, 150)
- Phone (Text, 20)
- Mobile (Text, 20)
- Email (Text, 100)
- Address (Text, 300)
- TaxNumber (Text, 20)
- SupplierType (Text, 50)
- IsActive (Yes/No)

### جدول الفواتير (Invoices)
- InvoiceID (Primary Key, AutoNumber)
- SupplierID (Foreign Key)
- ProjectID (Foreign Key)
- InvoiceNumber (Text, 30)
- InvoiceDate (Date/Time)
- TotalAmount (Currency)
- TaxAmount (Currency)
- NetAmount (Currency)
- InvoiceStatus (Text, 20) - معتمد، قيد المراجعة، مدفوع
- DueDate (Date/Time)
- ReceivedBy (Foreign Key - Users)
- Notes (Memo)

### جدول تفاصيل الفواتير (InvoiceDetails)
- DetailID (Primary Key, AutoNumber)
- InvoiceID (Foreign Key)
- ItemDescription (Text, 200)
- Quantity (Number, Single)
- UnitPrice (Currency)
- TotalPrice (Currency)

### جدول مدفوعات الموردين (SupplierPayments)
- PaymentID (Primary Key, AutoNumber)
- InvoiceID (Foreign Key)
- PaymentDate (Date/Time)
- PaymentAmount (Currency)
- PaymentMethod (Text, 20)
- CheckNumber (Text, 50)
- BankName (Text, 100)
- PaidBy (Foreign Key - Users)
- Notes (Memo)

## 6. جداول المشتريات

### جدول طلبات الشراء (PurchaseRequests)
- RequestID (Primary Key, AutoNumber)
- ProjectID (Foreign Key)
- RequestDate (Date/Time)
- RequestedBy (Foreign Key - Users)
- RequestStatus (Text, 20) - قيد المراجعة، معتمد، مرفوض، مكتمل
- TotalEstimatedCost (Currency)
- ApprovedBy (Foreign Key - Users)
- ApprovalDate (Date/Time)
- Notes (Memo)

### جدول تفاصيل طلبات الشراء (PurchaseRequestDetails)
- DetailID (Primary Key, AutoNumber)
- RequestID (Foreign Key)
- ItemDescription (Text, 200)
- Quantity (Number, Single)
- EstimatedUnitPrice (Currency)
- EstimatedTotalPrice (Currency)
- Urgency (Text, 20) - عادي، مستعجل، طارئ

## 7. جداول الصيانة والتشغيل

### جدول أعمال الصيانة (Maintenance)
- MaintenanceID (Primary Key, AutoNumber)
- ProjectID (Foreign Key)
- UnitID (Foreign Key) - اختياري
- MaintenanceType (Text, 50) - وقائية، إصلاحية، طارئة
- Description (Memo)
- RequestDate (Date/Time)
- ScheduledDate (Date/Time)
- CompletionDate (Date/Time)
- MaintenanceStatus (Text, 20) - مطلوب، قيد التنفيذ، مكتمل
- Cost (Currency)
- AssignedTo (Text, 100)
- RequestedBy (Foreign Key - Users)
- Notes (Memo)

### جدول التكاليف التشغيلية (OperatingCosts)
- CostID (Primary Key, AutoNumber)
- ProjectID (Foreign Key)
- CostType (Text, 50) - كهرباء، مياه، أمن، نظافة
- CostDate (Date/Time)
- Amount (Currency)
- Description (Text, 200)
- RecordedBy (Foreign Key - Users)
- Notes (Memo)

## 8. جداول المهام اليومية

### جدول المهام (Tasks)
- TaskID (Primary Key, AutoNumber)
- TaskTitle (Text, 200)
- TaskDescription (Memo)
- AssignedTo (Foreign Key - Users)
- AssignedBy (Foreign Key - Users)
- ProjectID (Foreign Key) - اختياري
- TaskPriority (Text, 20) - منخفض، متوسط، عالي، عاجل
- TaskStatus (Text, 20) - جديد، قيد التنفيذ، مكتمل، ملغي
- StartDate (Date/Time)
- DueDate (Date/Time)
- CompletionDate (Date/Time)
- CreatedDate (Date/Time)
- Notes (Memo)

## 9. جداول النظام

### جدول سجل النشاطات (ActivityLog)
- LogID (Primary Key, AutoNumber)
- UserID (Foreign Key)
- ActivityType (Text, 50)
- TableName (Text, 50)
- RecordID (Number, Long Integer)
- ActivityDescription (Text, 300)
- ActivityDate (Date/Time)
- IPAddress (Text, 15)

### جدول إعدادات النظام (SystemSettings)
- SettingID (Primary Key, AutoNumber)
- SettingName (Text, 100)
- SettingValue (Text, 500)
- SettingDescription (Text, 300)
- ModifiedBy (Foreign Key - Users)
- ModifiedDate (Date/Time)

## العلاقات الرئيسية

1. Users → Permissions (One-to-Many)
2. Projects → Units (One-to-Many)
3. Customers → Contracts (One-to-Many)
4. Units → Contracts (One-to-Many)
5. Contracts → Payments (One-to-Many)
6. Projects → ContractorContracts (One-to-Many)
7. Contractors → ContractorContracts (One-to-Many)
8. ContractorContracts → Extracts (One-to-Many)
9. Extracts → ContractorPayments (One-to-Many)
10. Suppliers → Invoices (One-to-Many)
11. Projects → Invoices (One-to-Many)
12. Invoices → InvoiceDetails (One-to-Many)
13. Invoices → SupplierPayments (One-to-Many)
14. Projects → PurchaseRequests (One-to-Many)
15. PurchaseRequests → PurchaseRequestDetails (One-to-Many)
16. Projects → Maintenance (One-to-Many)
17. Units → Maintenance (One-to-Many)
18. Projects → OperatingCosts (One-to-Many)
19. Users → Tasks (One-to-Many) - AssignedTo
20. Users → Tasks (One-to-Many) - AssignedBy
21. Projects → Tasks (One-to-Many)
