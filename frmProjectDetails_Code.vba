' كود نموذج تفاصيل المشروع
' Project Details Form Code

Option Compare Database
Option Explicit

Private IsNewRecord As Boolean

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "تفاصيل المشروع"
    Me.RightToLeft = True
    
    ' تحديد نوع العملية
    IsNewRecord = (Me.DataEntry = True)
    
    If IsNewRecord Then
        Me.Caption = "إضافة مشروع جديد"
        ' تعيين القيم الافتراضية
        Me.txtProjectStatus = "قيد التنفيذ"
        Me.txtCompletionPercentage = 0
        Me.txtCreatedBy = CurrentUserID
        Me.txtCreatedDate = Now()
    Else
        Me.Caption = "تفاصيل المشروع: " & Me.txtProjectName
    End If
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث قوائم الاختيار
    UpdateComboBoxes
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم ونوع العملية
    Dim CanEdit As Boolean
    
    If IsNewRecord Then
        CanEdit = CheckPermission("المشاريع", "CanAdd")
    Else
        CanEdit = CheckPermission("المشاريع", "CanEdit")
    End If
    
    ' تعيين حالة الحقول
    Me.txtProjectName.Enabled = CanEdit
    Me.txtProjectLocation.Enabled = CanEdit
    Me.cboProjectType.Enabled = CanEdit
    Me.txtTotalCost.Enabled = CanEdit
    Me.txtStartDate.Enabled = CanEdit
    Me.txtExpectedEndDate.Enabled = CanEdit
    Me.txtActualEndDate.Enabled = CanEdit
    Me.txtCompletionPercentage.Enabled = CanEdit
    Me.cboProjectStatus.Enabled = CanEdit
    Me.txtDescription.Enabled = CanEdit
    
    ' أزرار الحفظ والإلغاء
    Me.cmdSave.Enabled = CanEdit
    Me.cmdCancel.Enabled = True
    Me.cmdClose.Enabled = True
End Sub

Private Sub UpdateComboBoxes()
    ' تحديث قائمة أنواع المشاريع
    Me.cboProjectType.RowSource = "'سكني';'تجاري';'إداري';'مختلط'"
    
    ' تحديث قائمة حالات المشروع
    Me.cboProjectStatus.RowSource = "'قيد التنفيذ';'مكتمل';'متوقف';'ملغي'"
End Sub

Private Sub cmdSave_Click()
    ' حفظ بيانات المشروع
    On Error GoTo ErrorHandler
    
    ' التحقق من صحة البيانات
    If Not ValidateData() Then
        Exit Sub
    End If
    
    ' حفظ البيانات
    If Me.Dirty Then
        Me.Dirty = False
        
        If IsNewRecord Then
            ' تسجيل النشاط للإضافة
            LogActivity "إضافة", "Projects", Me.txtProjectID, "إضافة مشروع جديد: " & Me.txtProjectName
            MsgBox "تم إضافة المشروع بنجاح", vbInformation, "تم الحفظ"
        Else
            ' تسجيل النشاط للتعديل
            LogActivity "تعديل", "Projects", Me.txtProjectID, "تعديل مشروع: " & Me.txtProjectName
            MsgBox "تم حفظ التعديلات بنجاح", vbInformation, "تم الحفظ"
        End If
        
        ' إغلاق النموذج
        DoCmd.Close acForm, Me.Name
    End If
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء حفظ البيانات: " & Err.Description, vbCritical, "خطأ"
End Sub

Private Function ValidateData() As Boolean
    ValidateData = True
    
    ' التحقق من اسم المشروع
    If Not ValidateRequired(Me.txtProjectName, "اسم المشروع") Then
        Me.txtProjectName.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من موقع المشروع
    If Not ValidateRequired(Me.txtProjectLocation, "موقع المشروع") Then
        Me.txtProjectLocation.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من نوع المشروع
    If IsNull(Me.cboProjectType) Then
        MsgBox "يجب اختيار نوع المشروع", vbExclamation, "بيانات مطلوبة"
        Me.cboProjectType.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من التكلفة الإجمالية
    If Not IsNull(Me.txtTotalCost) And Me.txtTotalCost < 0 Then
        MsgBox "التكلفة الإجمالية يجب أن تكون أكبر من أو تساوي صفر", vbExclamation, "خطأ في البيانات"
        Me.txtTotalCost.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من نسبة الإنجاز
    If Not IsNull(Me.txtCompletionPercentage) Then
        If Me.txtCompletionPercentage < 0 Or Me.txtCompletionPercentage > 100 Then
            MsgBox "نسبة الإنجاز يجب أن تكون بين 0 و 100", vbExclamation, "خطأ في البيانات"
            Me.txtCompletionPercentage.SetFocus
            ValidateData = False
            Exit Function
        End If
    End If
    
    ' التحقق من التواريخ
    If Not IsNull(Me.txtStartDate) And Not IsNull(Me.txtExpectedEndDate) Then
        If Me.txtStartDate > Me.txtExpectedEndDate Then
            MsgBox "تاريخ البداية يجب أن يكون قبل التاريخ المتوقع للانتهاء", vbExclamation, "خطأ في البيانات"
            Me.txtStartDate.SetFocus
            ValidateData = False
            Exit Function
        End If
    End If
    
    If Not IsNull(Me.txtActualEndDate) And Not IsNull(Me.txtStartDate) Then
        If Me.txtActualEndDate < Me.txtStartDate Then
            MsgBox "تاريخ الانتهاء الفعلي يجب أن يكون بعد تاريخ البداية", vbExclamation, "خطأ في البيانات"
            Me.txtActualEndDate.SetFocus
            ValidateData = False
            Exit Function
        End If
    End If
    
    ' التحقق من حالة المشروع
    If IsNull(Me.cboProjectStatus) Then
        MsgBox "يجب اختيار حالة المشروع", vbExclamation, "بيانات مطلوبة"
        Me.cboProjectStatus.SetFocus
        ValidateData = False
        Exit Function
    End If
    
    ' التحقق من تطابق حالة المشروع مع نسبة الإنجاز
    If Me.cboProjectStatus = "مكتمل" And Me.txtCompletionPercentage < 100 Then
        If MsgBox("حالة المشروع 'مكتمل' ولكن نسبة الإنجاز أقل من 100%. هل تريد تعديل نسبة الإنجاز إلى 100%؟", _
                  vbYesNo + vbQuestion, "تأكيد البيانات") = vbYes Then
            Me.txtCompletionPercentage = 100
        End If
    End If
    
    ' تعيين تاريخ الانتهاء الفعلي إذا كان المشروع مكتملاً
    If Me.cboProjectStatus = "مكتمل" And IsNull(Me.txtActualEndDate) Then
        Me.txtActualEndDate = Date
    End If
End Function

Private Sub cmdCancel_Click()
    ' إلغاء التعديلات
    If Me.Dirty Then
        If MsgBox("هل تريد إلغاء التعديلات؟", vbYesNo + vbQuestion, "إلغاء التعديلات") = vbYes Then
            Me.Undo
            DoCmd.Close acForm, Me.Name
        End If
    Else
        DoCmd.Close acForm, Me.Name
    End If
End Sub

Private Sub cmdClose_Click()
    ' إغلاق النموذج
    If Me.Dirty Then
        Select Case MsgBox("هل تريد حفظ التعديلات قبل الإغلاق؟", vbYesNoCancel + vbQuestion, "حفظ التعديلات")
            Case vbYes
                cmdSave_Click
            Case vbNo
                Me.Undo
                DoCmd.Close acForm, Me.Name
            Case vbCancel
                ' لا تفعل شيئاً
        End Select
    Else
        DoCmd.Close acForm, Me.Name
    End If
End Sub

Private Sub txtCompletionPercentage_AfterUpdate()
    ' تحديث حالة المشروع تلقائياً حسب نسبة الإنجاز
    If Not IsNull(Me.txtCompletionPercentage) Then
        If Me.txtCompletionPercentage = 100 Then
            Me.cboProjectStatus = "مكتمل"
            If IsNull(Me.txtActualEndDate) Then
                Me.txtActualEndDate = Date
            End If
        ElseIf Me.txtCompletionPercentage > 0 And Me.cboProjectStatus <> "قيد التنفيذ" Then
            Me.cboProjectStatus = "قيد التنفيذ"
        End If
    End If
End Sub

Private Sub cboProjectStatus_AfterUpdate()
    ' تحديث نسبة الإنجاز تلقائياً حسب حالة المشروع
    If Not IsNull(Me.cboProjectStatus) Then
        Select Case Me.cboProjectStatus
            Case "مكتمل"
                If IsNull(Me.txtCompletionPercentage) Or Me.txtCompletionPercentage < 100 Then
                    Me.txtCompletionPercentage = 100
                End If
                If IsNull(Me.txtActualEndDate) Then
                    Me.txtActualEndDate = Date
                End If
            Case "ملغي", "متوقف"
                If IsNull(Me.txtActualEndDate) Then
                    Me.txtActualEndDate = Date
                End If
        End Select
    End If
End Sub

Private Sub txtTotalCost_AfterUpdate()
    ' تنسيق عرض التكلفة
    If Not IsNull(Me.txtTotalCost) Then
        Me.lblFormattedCost.Caption = FormatCurrency(Me.txtTotalCost)
    Else
        Me.lblFormattedCost.Caption = ""
    End If
End Sub

Private Sub Form_BeforeUpdate(Cancel As Integer)
    ' التحقق من البيانات قبل الحفظ
    If Not ValidateData() Then
        Cancel = True
    End If
End Sub

Private Sub Form_AfterUpdate()
    ' تحديث العنوان بعد الحفظ
    If Not IsNewRecord Then
        Me.Caption = "تفاصيل المشروع: " & Me.txtProjectName
    End If
End Sub

Private Sub Form_KeyDown(KeyCode As Integer, Shift As Integer)
    ' اختصارات لوحة المفاتيح
    Select Case KeyCode
        Case vbKeyS
            If Shift = acCtrlMask Then ' Ctrl+S للحفظ
                cmdSave_Click
                KeyCode = 0
            End If
        Case vbKeyEscape ' Escape للإلغاء
            cmdCancel_Click
            KeyCode = 0
    End Select
End Sub
