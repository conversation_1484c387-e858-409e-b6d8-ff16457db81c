' كود نموذج إدارة المشتريات
' Purchases Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    Me.Caption = "إدارة المشتريات"
    Me.RightToLeft = True
    SetFormPermissions
    RefreshData
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    Me.cmdAddRequest.Enabled = CheckPermission("المشتريات", "CanAdd")
    Me.cmdEditRequest.Enabled = CheckPermission("المشتريات", "CanEdit")
    Me.cmdDeleteRequest.Enabled = CheckPermission("المشتريات", "CanDelete")
    Me.cmdApproveRequest.Enabled = CheckPermission("المشتريات", "CanEdit")
    Me.cmdPrint.Enabled = CheckPermission("المشتريات", "CanPrint")
End Sub

Private Sub RefreshData()
    Me.lstPurchaseRequests.RowSource = "SELECT pr.RequestID, u.FullName AS RequestedBy, " & _
                                       "p.ProjectName, pr.TotalEstimatedCost, pr.RequestStatus " & _
                                       "FROM ((PurchaseRequests pr " & _
                                       "INNER JOIN Users u ON pr.RequestedBy = u.UserID) " & _
                                       "LEFT JOIN Projects p ON pr.ProjectID = p.ProjectID) " & _
                                       "ORDER BY pr.RequestDate DESC"
    Me.lstPurchaseRequests.Requery
End Sub

Private Sub UpdateStatistics()
    Me.lblTotalRequests.Caption = DCount("*", "PurchaseRequests")
    Me.lblPendingRequests.Caption = DCount("*", "PurchaseRequests", "RequestStatus = 'قيد المراجعة'")
    Me.lblApprovedRequests.Caption = DCount("*", "PurchaseRequests", "RequestStatus = 'معتمد'")
    Me.lblCompletedRequests.Caption = DCount("*", "PurchaseRequests", "RequestStatus = 'مكتمل'")
End Sub

Private Sub cmdAddRequest_Click()
    If Not CheckPermission("المشتريات", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة طلب شراء جديد", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmPurchaseRequestDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditRequest_Click()
    If IsNull(Me.lstPurchaseRequests) Then
        MsgBox "يرجى اختيار طلب للتعديل", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmPurchaseRequestDetails", acNormal, , "RequestID = " & Me.lstPurchaseRequests, acFormEdit
End Sub

Private Sub cmdApproveRequest_Click()
    If IsNull(Me.lstPurchaseRequests) Then
        MsgBox "يرجى اختيار طلب للاعتماد", vbExclamation
        Exit Sub
    End If
    
    If MsgBox("هل تريد اعتماد هذا الطلب؟", vbYesNo + vbQuestion) = vbYes Then
        CurrentDb.Execute "UPDATE PurchaseRequests SET RequestStatus = 'معتمد', " & _
                          "ApprovedBy = " & CurrentUserID & ", ApprovalDate = Now() " & _
                          "WHERE RequestID = " & Me.lstPurchaseRequests
        RefreshData
        UpdateStatistics
        MsgBox "تم اعتماد الطلب بنجاح", vbInformation
    End If
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Purchases", 0, "إغلاق نموذج إدارة المشتريات"
End Sub
