# دليل المستخدم - نظام رافع للتطوير العقاري
# User Manual - Rafea Real Estate Development System

## 1. مقدمة عن النظام

### نظرة عامة
نظام رافع للتطوير العقاري هو نظام إداري ومحاسبي متكامل مصمم خصيصاً لإدارة شركات التطوير العقاري. يوفر النظام إدارة شاملة للمشاريع، المبيعات، المقاولين، الموردين، والعمليات اليومية.

### الميزات الرئيسية
- إدارة المشاريع العقارية والوحدات السكنية
- نظام مبيعات متكامل مع إدارة العملاء والعقود
- إدارة المقاولين والمستخلصات
- إدارة الموردين والفواتير
- نظام المشتريات وطلبات الشراء
- إدارة الصيانة والتكاليف التشغيلية
- نظام المهام اليومية
- تقارير شاملة وقابلة للتخصيص
- نظام صلاحيات متقدم
- العمل على الشبكة المحلية

## 2. بدء الاستخدام

### تسجيل الدخول
1. افتح النظام من اختصار سطح المكتب
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط "تسجيل الدخول"

### الواجهة الرئيسية
بعد تسجيل الدخول، ستظهر الواجهة الرئيسية التي تحتوي على:
- لوحة التحكم مع الإحصائيات الرئيسية
- قائمة الوحدات الأساسية
- معلومات المستخدم الحالي
- التاريخ والوقت الحالي

## 3. إدارة المشاريع

### إضافة مشروع جديد
1. اضغط على "إدارة المشاريع" من الواجهة الرئيسية
2. اضغط "إضافة مشروع جديد"
3. املأ البيانات المطلوبة:
   - اسم المشروع
   - موقع المشروع
   - نوع المشروع (سكني، تجاري، إداري، مختلط)
   - التكلفة الإجمالية
   - تاريخ البداية والانتهاء المتوقع
   - وصف المشروع
4. اضغط "حفظ"

### تعديل مشروع
1. اختر المشروع من القائمة
2. اضغط "تعديل" أو انقر نقراً مزدوجاً
3. عدّل البيانات المطلوبة
4. اضغط "حفظ"

### إدارة الوحدات السكنية
1. اختر المشروع
2. اضغط "إدارة الوحدات"
3. أضف الوحدات مع تحديد:
   - رقم الوحدة
   - نوع الوحدة (شقة، فيلا، محل)
   - المساحة وعدد الغرف
   - السعر
   - الحالة (متاح، محجوز، مباع)

## 4. إدارة المبيعات

### إضافة عميل جديد
1. اضغط "إدارة المبيعات"
2. اضغط "إضافة عميل جديد"
3. املأ بيانات العميل:
   - الاسم الكامل
   - أرقام الهاتف
   - البريد الإلكتروني
   - العنوان
   - الرقم الوطني (للأفراد) أو رقم السجل التجاري (للشركات)
   - نوع العميل (فرد أو شركة)

### إنشاء عقد جديد
1. اختر العميل
2. اضغط "إضافة عقد جديد"
3. اختر الوحدة المراد بيعها
4. حدد تفاصيل العقد:
   - المبلغ الإجمالي
   - الدفعة المقدمة
   - قيمة القسط الشهري
   - عدد الأقساط
5. احفظ العقد

### تسجيل المدفوعات
1. اختر العقد
2. اضغط "إضافة دفعة"
3. أدخل تفاصيل الدفعة:
   - المبلغ
   - طريقة الدفع
   - رقم الشيك (إن وجد)
   - اسم البنك
4. احفظ الدفعة

## 5. إدارة المقاولين

### إضافة مقاول جديد
1. اضغط "إدارة المقاولين"
2. اضغط "إضافة مقاول جديد"
3. أدخل بيانات المقاول:
   - اسم المقاول
   - اسم الشركة
   - بيانات الاتصال
   - الرقم الضريبي
   - التخصص

### إنشاء عقد مقاول
1. اختر المقاول
2. اضغط "إضافة عقد جديد"
3. حدد المشروع ونوع العمل
4. أدخل قيمة العقد وتواريخ التنفيذ
5. احفظ العقد

### إدارة المستخلصات
1. اختر عقد المقاول
2. اضغط "إضافة مستخلص"
3. أدخل تفاصيل المستخلص:
   - رقم المستخلص
   - قيمة المستخلص
   - نسبة العمل المنجز
4. احفظ المستخلص
5. انتظر الاعتماد من المدير

## 6. إدارة الموردين

### إضافة مورد جديد
1. اضغط "إدارة الموردين"
2. اضغط "إضافة مورد جديد"
3. أدخل بيانات المورد
4. احفظ البيانات

### إدارة الفواتير
1. اضغط "إضافة فاتورة جديدة"
2. اختر المورد والمشروع
3. أدخل تفاصيل الفاتورة:
   - رقم الفاتورة
   - تاريخ الفاتورة
   - المبلغ الإجمالي
   - مبلغ الضريبة
4. أضف تفاصيل الأصناف
5. احفظ الفاتورة

## 7. إدارة المشتريات

### إنشاء طلب شراء
1. اضغط "إدارة المشتريات"
2. اضغط "إضافة طلب جديد"
3. حدد المشروع (إن وجد)
4. أضف أصناف الطلب:
   - وصف الصنف
   - الكمية المطلوبة
   - السعر المتوقع
   - درجة الأولوية
5. احفظ الطلب

### متابعة الطلبات
- يمكن متابعة حالة الطلبات (قيد المراجعة، معتمد، مرفوض، مكتمل)
- يتم إشعار المستخدم عند تغيير حالة الطلب

## 8. إدارة الصيانة

### تسجيل عمل صيانة
1. اضغط "إدارة الصيانة"
2. اضغط "إضافة عمل صيانة"
3. حدد المشروع والوحدة (إن وجدت)
4. اختر نوع الصيانة (وقائية، إصلاحية، طارئة)
5. أدخل وصف العمل المطلوب
6. حدد التاريخ المجدول والتكلفة المتوقعة
7. احفظ البيانات

### تسجيل التكاليف التشغيلية
1. اضغط "إضافة تكلفة تشغيلية"
2. حدد المشروع ونوع التكلفة
3. أدخل المبلغ والوصف
4. احفظ البيانات

## 9. إدارة المهام

### إضافة مهمة جديدة
1. اضغط "إدارة المهام"
2. اضغط "إضافة مهمة جديدة"
3. أدخل تفاصيل المهمة:
   - عنوان المهمة
   - الوصف التفصيلي
   - المكلف بالمهمة
   - المشروع المرتبط (إن وجد)
   - الأولوية (منخفض، متوسط، عالي، عاجل)
   - تاريخ الاستحقاق
4. احفظ المهمة

### متابعة المهام الشخصية
- في قسم "مهامي"، يمكن رؤية جميع المهام المكلف بها
- يمكن تغيير حالة المهمة (جديد، قيد التنفيذ، مكتمل)
- يظهر تنبيه للمهام المتأخرة

## 10. التقارير

### إنشاء تقرير
1. اضغط "التقارير"
2. اختر فئة التقرير من القائمة
3. اختر التقرير المطلوب
4. حدد معايير التصفية (التواريخ، المشروع، الحالة)
5. اختر نوع الإخراج:
   - معاينة على الشاشة
   - طباعة مباشرة
   - تصدير PDF
   - تصدير Excel

### أنواع التقارير المتاحة
- تقارير المشاريع والوحدات
- تقارير المبيعات والعملاء
- تقارير المقاولين والمستخلصات
- تقارير الموردين والفواتير
- تقارير المشتريات
- تقارير الصيانة والتشغيل
- تقارير المهام
- التقارير المالية
- التقارير الإحصائية

## 11. إدارة المستخدمين (للمدير فقط)

### إضافة مستخدم جديد
1. اضغط "إدارة المستخدمين"
2. اضغط "إضافة مستخدم جديد"
3. أدخل بيانات المستخدم:
   - اسم المستخدم
   - كلمة المرور
   - الاسم الكامل
   - نوع المستخدم (مدير، محاسب، مهندس، مبيعات)
4. احفظ البيانات

### إدارة الصلاحيات
1. اختر المستخدم
2. اضغط "إدارة الصلاحيات"
3. حدد الصلاحيات لكل وحدة:
   - عرض
   - إضافة
   - تعديل
   - حذف
   - طباعة
4. احفظ الصلاحيات

## 12. النسخ الاحتياطي (للمدير فقط)

### إنشاء نسخة احتياطية
1. اضغط "نسخ احتياطي" من الواجهة الرئيسية
2. أكد العملية
3. انتظر حتى اكتمال النسخ
4. ستظهر رسالة تأكيد مع مسار الملف

### النسخ الاحتياطي التلقائي
- يتم إنشاء نسخة احتياطية تلقائياً كل يوم في الساعة 11:00 مساءً
- يتم الاحتفاظ بـ 30 نسخة احتياطية
- يتم حذف النسخ الأقدم من شهر تلقائياً

## 13. نصائح للاستخدام الأمثل

### الأمان
- لا تشارك كلمة المرور مع الآخرين
- سجل الخروج عند الانتهاء من العمل
- تأكد من حفظ البيانات قبل إغلاق النماذج

### الأداء
- أغلق النماذج غير المستخدمة
- استخدم البحث والتصفية لتقليل البيانات المعروضة
- تجنب فتح عدة تقارير في نفس الوقت

### البيانات
- تأكد من دقة البيانات المدخلة
- استخدم التواريخ بالتنسيق الصحيح
- راجع البيانات قبل الحفظ النهائي

## 14. حل المشاكل الشائعة

### مشكلة: لا يمكن تسجيل الدخول
**الحل:**
- تأكد من صحة اسم المستخدم وكلمة المرور
- تأكد من أن المستخدم نشط
- اتصل بالمدير لإعادة تعيين كلمة المرور

### مشكلة: بطء في النظام
**الحل:**
- تأكد من قوة إشارة Wi-Fi
- أغلق البرامج غير الضرورية
- أعد تشغيل التطبيق

### مشكلة: خطأ في حفظ البيانات
**الحل:**
- تأكد من اتصال الشبكة
- تحقق من صحة البيانات المدخلة
- تأكد من وجود الصلاحيات المطلوبة

### مشكلة: لا تظهر التقارير
**الحل:**
- تأكد من وجود بيانات للفترة المحددة
- تحقق من معايير التصفية
- تأكد من صلاحيات الطباعة

## 15. الدعم الفني

### للحصول على المساعدة:
- راجع هذا الدليل أولاً
- اتصل بمدير النظام
- سجل المشكلة في نظام المهام
- احتفظ برقم الخطأ (إن وجد) للمساعدة في الحل

### معلومات النظام:
- اسم النظام: نظام رافع للتطوير العقاري
- الإصدار: 1.0
- تاريخ الإصدار: 2024
- المطور: فريق التطوير - شركة رافع للتطوير العقاري
