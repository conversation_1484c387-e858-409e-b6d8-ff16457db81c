# دليل المستخدم - نظام رافع للتطوير العقاري
# User Manual - Rafea Real Estate Development System

## 📋 جدول المحتويات | Table of Contents

1. [مقدمة عن النظام](#مقدمة-عن-النظام)
2. [متطلبات النظام](#متطلبات-النظام)
3. [تسجيل الدخول](#تسجيل-الدخول)
4. [لوحة التحكم الرئيسية](#لوحة-التحكم-الرئيسية)
5. [إدارة المشاريع](#إدارة-المشاريع)
6. [إدارة العملاء](#إدارة-العملاء)
7. [إدارة المبيعات](#إدارة-المبيعات)
8. [إدارة المقاولين](#إدارة-المقاولين)
9. [إدارة الموردين](#إدارة-الموردين)
10. [التقارير](#التقارير)
11. [الصيانة والنسخ الاحتياطي](#الصيانة-والنسخ-الاحتياطي)
12. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🏢 مقدمة عن النظام

**نظام رافع للتطوير العقاري** هو نظام إداري ومحاسبي متكامل مُصمم خصيصاً لشركات التطوير العقاري. يوفر النظام إدارة شاملة لجميع جوانب العمل من المشاريع والمبيعات إلى المقاولين والموردين.

### ✨ الميزات الرئيسية:
- **إدارة شاملة للمشاريع** العقارية والوحدات السكنية
- **نظام مبيعات متكامل** مع إدارة العقود والمدفوعات
- **إدارة المقاولين** والمستخلصات والمدفوعات المرحلية
- **إدارة الموردين** والفواتير وسجلات الدفع
- **نظام المشتريات** وطلبات الشراء والموافقات
- **إدارة الصيانة** والتكاليف التشغيلية
- **نظام المهام اليومية** وتتبع الأداء
- **تقارير شاملة** قابلة للطباعة والتصدير
- **نظام صلاحيات متقدم** (4 أنواع مستخدمين)
- **دعم الشبكة المحلية** للعمل الجماعي
- **نسخ احتياطي تلقائي** لحماية البيانات

---

## 💻 متطلبات النظام

### المتطلبات الأساسية:
- **نظام التشغيل:** Windows 10 أو أحدث
- **Microsoft Access:** 2016 أو أحدث
- **الذاكرة:** 4 GB RAM كحد أدنى
- **مساحة القرص:** 500 MB مساحة فارغة
- **الشبكة:** اتصال Wi-Fi للعمل الجماعي (اختياري)

### للعمل على الشبكة المحلية:
- **خادم رئيسي** مع Windows Server أو Windows 10 Pro
- **مشاركة ملفات** مُفعلة على الشبكة
- **صلاحيات قراءة/كتابة** لجميع المستخدمين

---

## 🔐 تسجيل الدخول

### الحسابات الافتراضية:

| نوع المستخدم | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------------|-------------|------------|----------|
| **مدير النظام** | `admin` | `admin123` | جميع الصلاحيات |
| **محاسب** | `accountant` | `123456` | المالية والتقارير |
| **مهندس** | `engineer` | `123456` | المشاريع والمقاولين |
| **مبيعات** | `sales` | `123456` | العملاء والمبيعات |

### خطوات تسجيل الدخول:

1. **افتح ملف النظام:** `RafeaSystem_Simple.accdb`
2. **اضغط Ctrl+G** لفتح نافذة Immediate
3. **اكتب الأمر:** `LoginUser("admin", "admin123")`
4. **اضغط Enter** لتسجيل الدخول

⚠️ **مهم:** قم بتغيير كلمات المرور الافتراضية فور تسجيل الدخول الأول!

---

## 📊 لوحة التحكم الرئيسية

لوحة التحكم توفر نظرة شاملة على حالة النظام والإحصائيات المهمة.

### عرض لوحة التحكم:
```vba
ShowMainDashboard
```

### الإحصائيات المعروضة:
- **إجمالي المشاريع** والمشاريع النشطة
- **إجمالي العملاء** والعملاء الجدد
- **إجمالي الوحدات** (متاح/محجوز/مباع)
- **المقاولين والموردين** النشطين
- **إجمالي قيمة المشاريع** بالريال السعودي

### الوحدات المتاحة:
- ✅ إدارة المشاريع والوحدات السكنية
- ✅ إدارة العملاء والعقود والمبيعات
- ✅ إدارة المقاولين والمستخلصات
- ✅ إدارة الموردين والفواتير
- ✅ إدارة المشتريات وطلبات الشراء
- ✅ إدارة الصيانة والتكاليف التشغيلية
- ✅ إدارة المهام اليومية
- ✅ نظام التقارير الشامل

---

## 🏗️ إدارة المشاريع

وحدة إدارة المشاريع تتيح متابعة جميع المشاريع العقارية من البداية حتى التسليم.

### عرض قائمة المشاريع:
```vba
ShowProjects
```

### بيانات المشروع:
- **اسم المشروع** ووصف مختصر
- **موقع المشروع** والمدينة
- **نوع المشروع** (سكني/تجاري/إداري)
- **التكلفة الإجمالية** بالريال السعودي
- **نسبة الإنجاز** المئوية
- **حالة المشروع** (قيد التنفيذ/مكتمل/متوقف)
- **تاريخ البداية والانتهاء** المتوقع

### أنواع المشاريع:
- **سكني:** مجمعات سكنية وفلل وشقق
- **تجاري:** مراكز تسوق ومحلات تجارية
- **إداري:** مباني مكاتب ومجمعات أعمال
- **مختلط:** مشاريع تجمع أكثر من نوع

### إدارة الوحدات السكنية:
```vba
ShowUnits
```

#### بيانات الوحدة:
- **رقم الوحدة** والطابق
- **نوع الوحدة** (شقة/فيلا/مكتب/محل)
- **المساحة** بالمتر المربع
- **عدد الغرف والحمامات**
- **سعر الوحدة** بالريال السعودي
- **حالة الوحدة** (متاح/محجوز/مباع)

---

## 👥 إدارة العملاء

وحدة إدارة العملاء تتيح متابعة جميع العملاء والمهتمين بالمشاريع.

### عرض قائمة العملاء:
```vba
ShowCustomers
```

### بيانات العميل:
- **اسم العميل** الكامل
- **نوع العميل** (فرد/شركة)
- **أرقام الهواتف** (ثابت/جوال)
- **البريد الإلكتروني**
- **العنوان** الكامل
- **رقم الهوية** أو السجل التجاري
- **ملاحظات** إضافية

### أنواع العملاء:
- **فرد:** عملاء أفراد للوحدات السكنية
- **شركة:** شركات للوحدات التجارية والإدارية
- **مستثمر:** مستثمرون لشراء عدة وحدات
- **مطور:** شركات تطوير للشراكات

### إدارة العقود:
- **عقود البيع** مع تفاصيل الدفع
- **عقود الحجز** والدفعات المقدمة
- **جدولة الأقساط** الشهرية
- **متابعة المدفوعات** والمتأخرات

---

## 💰 إدارة المبيعات

وحدة المبيعات تدير العملية البيعية من الحجز حتى التسليم.

### عملية البيع:
1. **حجز الوحدة** ودفع العربون
2. **توقيع العقد** وتحديد شروط الدفع
3. **جدولة الأقساط** الشهرية أو الدورية
4. **متابعة المدفوعات** وإرسال التذكيرات
5. **إصدار الفواتير** والإيصالات
6. **تسليم الوحدة** عند اكتمال الدفع

### أنواع الدفع:
- **نقدي:** دفع كامل عند التوقيع
- **أقساط:** دفعات شهرية أو دورية
- **تمويل بنكي:** بالتعاون مع البنوك
- **مختلط:** مزيج من الطرق السابقة

### التقارير المالية:
- **تقرير المبيعات** اليومي/الشهري/السنوي
- **تقرير المدفوعات** والمتأخرات
- **تقرير الأرباح** والهوامش
- **تقرير التدفق النقدي**

---

## 🔨 إدارة المقاولين

وحدة المقاولين تدير العلاقة مع مقاولي التنفيذ والصيانة.

### بيانات المقاول:
- **اسم المقاول** واسم الشركة
- **التخصص** (خرسانة/كهرباء/سباكة/تشطيبات)
- **أرقام التواصل** والعنوان
- **رقم الترخيص** والسجل التجاري
- **تقييم الأداء** والمشاريع السابقة

### إدارة العقود:
- **عقود المقاولة** مع تفاصيل العمل
- **جدولة المراحل** والمواعيد النهائية
- **المستخلصات** والمدفوعات المرحلية
- **ضمانات الأداء** والجودة

### المستخلصات:
- **مستخلص أعمال** لكل مرحلة
- **نسبة الإنجاز** المحققة
- **قيمة المستخلص** والضرائب
- **اعتماد المهندس** المشرف
- **جدولة الدفع** والاستحقاقات

---

## 📦 إدارة الموردين

وحدة الموردين تدير المشتريات ومواد البناء والتجهيزات.

### بيانات المورد:
- **اسم المورد** واسم الشركة
- **نوع التوريد** (مواد بناء/تجهيزات/خدمات)
- **أرقام التواصل** والعنوان
- **الرقم الضريبي** وشروط الدفع
- **تقييم الجودة** والالتزام

### إدارة الفواتير:
- **فواتير المشتريات** مع التفاصيل
- **ضريبة القيمة المضافة** والخصومات
- **شروط الدفع** والاستحقاقات
- **متابعة المدفوعات** والمتأخرات

### طلبات الشراء:
- **طلب شراء** مواد أو خدمات
- **اعتماد الطلب** من المسؤول
- **مقارنة العروض** واختيار الأفضل
- **متابعة التوريد** والاستلام

---

## 📊 التقارير

نظام التقارير يوفر تقارير شاملة لجميع جوانب العمل.

### تقارير المشاريع:
- **تقرير حالة المشاريع** ونسب الإنجاز
- **تقرير التكاليف** والميزانيات
- **تقرير الجدولة الزمنية** والتأخيرات
- **تقرير الوحدات** والمبيعات

### تقارير المبيعات:
- **تقرير المبيعات** اليومي/الشهري
- **تقرير العملاء** والعقود
- **تقرير المدفوعات** والمتأخرات
- **تقرير الأرباح** والهوامش

### تقارير المقاولين:
- **تقرير أداء المقاولين**
- **تقرير المستخلصات** والمدفوعات
- **تقرير التأخيرات** والغرامات
- **تقرير التكاليف** الفعلية

### تقارير الموردين:
- **تقرير المشتريات** والفواتير
- **تقرير المدفوعات** للموردين
- **تقرير تقييم الموردين**
- **تقرير المخزون** والمواد

### خيارات التصدير:
- **PDF:** للطباعة والأرشفة
- **Excel:** للتحليل والمعالجة
- **Word:** للتقارير المفصلة
- **طباعة مباشرة** على الطابعة

---

## 🔧 الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي:
- **نسخ احتياطي يومي** تلقائي
- **نسخ احتياطي يدوي** عند الحاجة
- **حفظ النسخ** في مواقع متعددة
- **استعادة البيانات** عند الحاجة

### صيانة قاعدة البيانات:
- **ضغط قاعدة البيانات** لتوفير المساحة
- **إصلاح الأخطاء** التلقائي
- **تحسين الأداء** والفهارس
- **تنظيف البيانات** القديمة

### أمان البيانات:
- **كلمات مرور قوية** لجميع المستخدمين
- **صلاحيات محددة** لكل نوع مستخدم
- **تشفير البيانات** الحساسة
- **سجل العمليات** والتغييرات

---

## 🆘 استكشاف الأخطاء

### المشاكل الشائعة والحلول:

#### 1. بطء في النظام:
- **أغلق البرامج** غير الضرورية
- **اضغط قاعدة البيانات** من قائمة الصيانة
- **تحقق من مساحة القرص** الصلب
- **أعد تشغيل الجهاز** إذا لزم الأمر

#### 2. مشاكل الشبكة:
- **تحقق من الاتصال** بالشبكة
- **تأكد من الصلاحيات** على المجلد المشترك
- **أعد تشغيل الراوتر** إذا لزم الأمر
- **اتصل بمدير الشبكة** للمساعدة

#### 3. أخطاء في البيانات:
- **تحقق من صحة البيانات** المدخلة
- **استخدم النسخة الاحتياطية** إذا لزم الأمر
- **اتصل بالدعم الفني** للمساعدة

#### 4. مشاكل الطباعة:
- **تحقق من إعدادات الطابعة**
- **تأكد من توفر الحبر والورق**
- **جرب طباعة تجريبية** من برنامج آخر

### الدعم الفني:
- **راجع هذا الدليل** أولاً
- **تحقق من ملفات المساعدة** في النظام
- **اتصل بفريق الدعم** عند الحاجة

---

## 📞 معلومات الاتصال

**شركة رافع للتطوير العقاري**
- **الموقع الإلكتروني:** www.rafea-realestate.com
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966-11-1234567
- **العنوان:** الرياض، المملكة العربية السعودية

---

**© 2024 شركة رافع للتطوير العقاري - جميع الحقوق محفوظة**

*هذا الدليل يغطي الوظائف الأساسية للنظام. للحصول على تدريب متقدم أو دعم فني، يرجى الاتصال بفريق الدعم.*
