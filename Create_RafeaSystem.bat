@echo off
echo ========================================
echo    نظام رافع للتطوير العقاري
echo    Rafea Real Estate System Setup
echo ========================================
echo.

echo إنشاء مجلدات النظام...
echo Creating system directories...

if not exist "RafeaSystem" mkdir RafeaSystem
if not exist "RafeaSystem\Database" mkdir RafeaSystem\Database
if not exist "RafeaSystem\Backups" mkdir RafeaSystem\Backups
if not exist "RafeaSystem\Reports" mkdir RafeaSystem\Reports
if not exist "RafeaSystem\Logs" mkdir RafeaSystem\Logs

echo.
echo إنشاء ملف التكوين...
echo Creating configuration file...

echo ; ملف تكوين نظام رافع للتطوير العقاري > RafeaSystem\Config.ini
echo ; Rafea Real Estate System Configuration File >> RafeaSystem\Config.ini
echo. >> RafeaSystem\Config.ini
echo [Database] >> RafeaSystem\Config.ini
echo ServerPath=.\Database\ >> RafeaSystem\Config.ini
echo DatabaseName=RafeaSystem_BE.accdb >> RafeaSystem\Config.ini
echo BackupPath=.\Backups\ >> RafeaSystem\Config.ini
echo ConnectionTimeout=30 >> RafeaSystem\Config.ini
echo. >> RafeaSystem\Config.ini
echo [Network] >> RafeaSystem\Config.ini
echo ServerIP=127.0.0.1 >> RafeaSystem\Config.ini
echo ServerName=LOCAL-SERVER >> RafeaSystem\Config.ini
echo ShareName=RafeaDB >> RafeaSystem\Config.ini
echo. >> RafeaSystem\Config.ini
echo [Application] >> RafeaSystem\Config.ini
echo AppName=نظام رافع للتطوير العقاري >> RafeaSystem\Config.ini
echo Version=1.0 >> RafeaSystem\Config.ini
echo CompanyName=شركة رافع للتطوير العقاري >> RafeaSystem\Config.ini
echo. >> RafeaSystem\Config.ini
echo [Backup] >> RafeaSystem\Config.ini
echo AutoBackup=True >> RafeaSystem\Config.ini
echo BackupTime=23:00 >> RafeaSystem\Config.ini
echo RetentionDays=30 >> RafeaSystem\Config.ini

echo.
echo تم إنشاء هيكل النظام بنجاح!
echo System structure created successfully!
echo.
echo المجلدات المُنشأة:
echo Created directories:
echo - RafeaSystem\Database
echo - RafeaSystem\Backups  
echo - RafeaSystem\Reports
echo - RafeaSystem\Logs
echo.
echo الملفات المُنشأة:
echo Created files:
echo - RafeaSystem\Config.ini
echo.
echo ========================================
echo الخطوة التالية: إنشاء قاعدة البيانات
echo Next step: Create the database
echo ========================================
echo.
pause
