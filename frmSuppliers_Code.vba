' كود نموذج إدارة الموردين والفواتير
' Suppliers and Invoices Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "إدارة الموردين والفواتير"
    Me.RightToLeft = True
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث البيانات
    RefreshData
    
    ' تحديث الإحصائيات
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم
    Me.cmdAddSupplier.Enabled = CheckPermission("الموردين", "CanAdd")
    Me.cmdEditSupplier.Enabled = CheckPermission("الموردين", "CanEdit")
    Me.cmdDeleteSupplier.Enabled = CheckPermission("الموردين", "CanDelete")
    Me.cmdAddInvoice.Enabled = CheckPermission("الموردين", "CanAdd")
    Me.cmdEditInvoice.Enabled = CheckPermission("الموردين", "CanEdit")
    Me.cmdDeleteInvoice.Enabled = CheckPermission("الموردين", "CanDelete")
    Me.cmdApproveInvoice.Enabled = CheckPermission("الموردين", "CanEdit")
    Me.cmdAddPayment.Enabled = CheckPermission("الموردين", "CanAdd")
    Me.cmdPrint.Enabled = CheckPermission("الموردين", "CanPrint")
End Sub

Private Sub RefreshData()
    ' تحديث قائمة الموردين
    Me.lstSuppliers.RowSource = "SELECT SupplierID, SupplierName, CompanyName, Phone, " & _
                                "Mobile, SupplierType, IsActive " & _
                                "FROM Suppliers ORDER BY SupplierName"
    Me.lstSuppliers.Requery
    
    ' تحديث قائمة الفواتير
    Me.lstInvoices.RowSource = "SELECT i.InvoiceID, s.SupplierName, i.InvoiceNumber, " & _
                               "i.InvoiceDate, i.NetAmount, i.InvoiceStatus " & _
                               "FROM Invoices i INNER JOIN Suppliers s ON i.SupplierID = s.SupplierID " & _
                               "ORDER BY i.InvoiceDate DESC"
    Me.lstInvoices.Requery
End Sub

Private Sub UpdateStatistics()
    ' تحديث إحصائيات الموردين والفواتير
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إجمالي الموردين
    Me.lblTotalSuppliers.Caption = DCount("*", "Suppliers", "IsActive = True")
    
    ' إجمالي الفواتير
    Me.lblTotalInvoices.Caption = DCount("*", "Invoices")
    Me.lblPendingInvoices.Caption = DCount("*", "Invoices", "InvoiceStatus = 'قيد المراجعة'")
    Me.lblApprovedInvoices.Caption = DCount("*", "Invoices", "InvoiceStatus = 'معتمد'")
    Me.lblPaidInvoices.Caption = DCount("*", "Invoices", "InvoiceStatus = 'مدفوع'")
    
    ' إجمالي قيمة الفواتير
    Set rs = db.OpenRecordset("SELECT Sum(NetAmount) AS TotalInvoices FROM Invoices")
    If Not IsNull(rs!TotalInvoices) Then
        Me.lblTotalInvoicesValue.Caption = FormatCurrency(rs!TotalInvoices)
    Else
        Me.lblTotalInvoicesValue.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي المدفوعات
    Set rs = db.OpenRecordset("SELECT Sum(PaymentAmount) AS TotalPayments FROM SupplierPayments")
    If Not IsNull(rs!TotalPayments) Then
        Me.lblTotalPayments.Caption = FormatCurrency(rs!TotalPayments)
    Else
        Me.lblTotalPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' المبلغ المتبقي للدفع
    Set rs = db.OpenRecordset("SELECT Sum(i.NetAmount) - Nz(Sum(sp.PaymentAmount), 0) AS RemainingAmount " & _
                              "FROM Invoices i LEFT JOIN SupplierPayments sp ON i.InvoiceID = sp.InvoiceID " & _
                              "WHERE i.InvoiceStatus IN ('معتمد', 'مدفوع')")
    If Not IsNull(rs!RemainingAmount) Then
        Me.lblRemainingPayments.Caption = FormatCurrency(rs!RemainingAmount)
    Else
        Me.lblRemainingPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إدارة الموردين
Private Sub cmdAddSupplier_Click()
    If Not CheckPermission("الموردين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة مورد جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmSupplierDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditSupplier_Click()
    If Not CheckPermission("الموردين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل الموردين", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstSuppliers) Then
        MsgBox "يرجى اختيار مورد للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmSupplierDetails", acNormal, , "SupplierID = " & Me.lstSuppliers, acFormEdit
End Sub

Private Sub cmdDeleteSupplier_Click()
    If Not CheckPermission("الموردين", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف الموردين", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstSuppliers) Then
        MsgBox "يرجى اختيار مورد للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود فواتير للمورد
    If DCount("*", "Invoices", "SupplierID = " & Me.lstSuppliers) > 0 Then
        MsgBox "لا يمكن حذف هذا المورد لوجود فواتير مرتبطة به", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا المورد؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        CurrentDb.Execute "DELETE FROM Suppliers WHERE SupplierID = " & Me.lstSuppliers
        LogActivity "حذف", "Suppliers", Me.lstSuppliers, "حذف مورد"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف المورد بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

' إدارة الفواتير
Private Sub cmdAddInvoice_Click()
    If Not CheckPermission("الموردين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة فاتورة جديدة", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmInvoiceDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditInvoice_Click()
    If Not CheckPermission("الموردين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل الفواتير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmInvoiceDetails", acNormal, , "InvoiceID = " & Me.lstInvoices, acFormEdit
End Sub

Private Sub cmdDeleteInvoice_Click()
    If Not CheckPermission("الموردين", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف الفواتير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود مدفوعات للفاتورة
    If DCount("*", "SupplierPayments", "InvoiceID = " & Me.lstInvoices) > 0 Then
        MsgBox "لا يمكن حذف هذه الفاتورة لوجود مدفوعات مرتبطة بها", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذه الفاتورة؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        ' حذف تفاصيل الفاتورة أولاً
        CurrentDb.Execute "DELETE FROM InvoiceDetails WHERE InvoiceID = " & Me.lstInvoices
        ' حذف الفاتورة
        CurrentDb.Execute "DELETE FROM Invoices WHERE InvoiceID = " & Me.lstInvoices
        LogActivity "حذف", "Invoices", Me.lstInvoices, "حذف فاتورة"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف الفاتورة بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

Private Sub cmdApproveInvoice_Click()
    If Not CheckPermission("الموردين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لاعتماد الفواتير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة للاعتماد", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من حالة الفاتورة
    Dim InvoiceStatus As String
    InvoiceStatus = DLookup("InvoiceStatus", "Invoices", "InvoiceID = " & Me.lstInvoices)
    
    If InvoiceStatus <> "قيد المراجعة" Then
        MsgBox "يمكن اعتماد الفواتير التي في حالة 'قيد المراجعة' فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    If MsgBox("هل تريد اعتماد هذه الفاتورة؟", vbYesNo + vbQuestion, "اعتماد الفاتورة") = vbYes Then
        CurrentDb.Execute "UPDATE Invoices SET InvoiceStatus = 'معتمد' WHERE InvoiceID = " & Me.lstInvoices
        
        LogActivity "اعتماد", "Invoices", Me.lstInvoices, "اعتماد فاتورة"
        RefreshData
        UpdateStatistics
        MsgBox "تم اعتماد الفاتورة بنجاح", vbInformation, "تم الاعتماد"
    End If
End Sub

Private Sub cmdRejectInvoice_Click()
    If Not CheckPermission("الموردين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لرفض الفواتير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة للرفض", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من حالة الفاتورة
    Dim InvoiceStatus As String
    InvoiceStatus = DLookup("InvoiceStatus", "Invoices", "InvoiceID = " & Me.lstInvoices)
    
    If InvoiceStatus <> "قيد المراجعة" Then
        MsgBox "يمكن رفض الفواتير التي في حالة 'قيد المراجعة' فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    Dim RejectReason As String
    RejectReason = InputBox("أدخل سبب الرفض:", "رفض الفاتورة")
    
    If Len(Trim(RejectReason)) > 0 Then
        CurrentDb.Execute "UPDATE Invoices SET InvoiceStatus = 'مرفوض', Notes = '" & RejectReason & "' WHERE InvoiceID = " & Me.lstInvoices
        
        LogActivity "رفض", "Invoices", Me.lstInvoices, "رفض فاتورة: " & RejectReason
        RefreshData
        UpdateStatistics
        MsgBox "تم رفض الفاتورة", vbInformation, "تم الرفض"
    End If
End Sub

' إدارة المدفوعات
Private Sub cmdAddPayment_Click()
    If Not CheckPermission("الموردين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة دفعة جديدة", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من أن الفاتورة معتمدة
    Dim InvoiceStatus As String
    InvoiceStatus = DLookup("InvoiceStatus", "Invoices", "InvoiceID = " & Me.lstInvoices)
    
    If InvoiceStatus <> "معتمد" And InvoiceStatus <> "مدفوع" Then
        MsgBox "يمكن إضافة مدفوعات للفواتير المعتمدة فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmSupplierPaymentDetails", acNormal, , "InvoiceID = " & Me.lstInvoices, acFormAdd
End Sub

Private Sub cmdViewPayments_Click()
    If IsNull(Me.lstInvoices) Then
        MsgBox "يرجى اختيار فاتورة أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmSupplierPayments", acNormal, , "InvoiceID = " & Me.lstInvoices
End Sub

' البحث والتصفية
Private Sub cmdSearchSuppliers_Click()
    Dim SearchText As String
    SearchText = InputBox("أدخل نص البحث:", "البحث في الموردين")
    
    If Len(Trim(SearchText)) > 0 Then
        Me.lstSuppliers.RowSource = "SELECT SupplierID, SupplierName, CompanyName, Phone, " & _
                                    "Mobile, SupplierType, IsActive " & _
                                    "FROM Suppliers WHERE SupplierName LIKE '*" & SearchText & "*' OR " & _
                                    "CompanyName LIKE '*" & SearchText & "*' OR " & _
                                    "SupplierType LIKE '*" & SearchText & "*' " & _
                                    "ORDER BY SupplierName"
        Me.lstSuppliers.Requery
        
        If Me.lstSuppliers.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshData
        End If
    End If
End Sub

Private Sub cmdSearchInvoices_Click()
    Dim SearchText As String
    SearchText = InputBox("أدخل نص البحث:", "البحث في الفواتير")
    
    If Len(Trim(SearchText)) > 0 Then
        Me.lstInvoices.RowSource = "SELECT i.InvoiceID, s.SupplierName, i.InvoiceNumber, " & _
                                   "i.InvoiceDate, i.NetAmount, i.InvoiceStatus " & _
                                   "FROM Invoices i INNER JOIN Suppliers s ON i.SupplierID = s.SupplierID " & _
                                   "WHERE s.SupplierName LIKE '*" & SearchText & "*' OR " & _
                                   "i.InvoiceNumber LIKE '*" & SearchText & "*' " & _
                                   "ORDER BY i.InvoiceDate DESC"
        Me.lstInvoices.Requery
        
        If Me.lstInvoices.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshData
        End If
    End If
End Sub

' التقارير والطباعة
Private Sub cmdPrint_Click()
    If Not CheckPermission("الموردين", "CanPrint") Then
        MsgBox "ليس لديك صلاحية لطباعة التقارير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    ' فتح نموذج اختيار التقرير
    DoCmd.OpenForm "frmSuppliersReports", acDialog
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation, "تحديث"
End Sub

Private Sub lstSuppliers_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstSuppliers) Then
        cmdEditSupplier_Click
    End If
End Sub

Private Sub lstInvoices_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstInvoices) Then
        cmdEditInvoice_Click
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Suppliers", 0, "إغلاق نموذج إدارة الموردين"
End Sub
