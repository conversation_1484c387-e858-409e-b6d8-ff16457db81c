# إنشاء النماذج التفاعلية لنظام رافع
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "    إنشاء النماذج التفاعلية" -ForegroundColor Yellow
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_WithForms.accdb"

Write-Host "إنشاء نظام رافع مع النماذج التفاعلية..." -ForegroundColor Yellow
Write-Host "المسار: $systemPath" -ForegroundColor Cyan

try {
    # فتح Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    
    Write-Host "✓ تم فتح Microsoft Access" -ForegroundColor Green
    
    # إنشاء قاعدة بيانات جديدة
    $access.NewCurrentDatabase($systemPath)
    
    Write-Host "✓ تم إنشاء قاعدة البيانات" -ForegroundColor Green
    
    # الحصول على قاعدة البيانات الحالية
    $db = $access.CurrentDb()
    
    Write-Host "إنشاء الجداول..." -ForegroundColor Yellow
    
    # إنشاء جدول المستخدمين
    $sql = "CREATE TABLE Users (UserID COUNTER PRIMARY KEY, Username TEXT(50), Password TEXT(50), FullName TEXT(100), UserType TEXT(20), IsActive YESNO DEFAULT True);"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء جدول المشاريع
    $sql = "CREATE TABLE Projects (ProjectID COUNTER PRIMARY KEY, ProjectName TEXT(100), ProjectLocation TEXT(100), ProjectType TEXT(50), TotalCost CURRENCY, CompletionPercentage SINGLE, ProjectStatus TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول المشاريع" -ForegroundColor Green
    
    # إنشاء جدول العملاء
    $sql = "CREATE TABLE Customers (CustomerID COUNTER PRIMARY KEY, CustomerName TEXT(100), Phone TEXT(20), Mobile TEXT(20), Email TEXT(100), CustomerType TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول العملاء" -ForegroundColor Green
    
    # إنشاء جدول الوحدات
    $sql = "CREATE TABLE Units (UnitID COUNTER PRIMARY KEY, ProjectID LONG, UnitNumber TEXT(20), UnitType TEXT(50), Area SINGLE, UnitPrice CURRENCY, UnitStatus TEXT(20));"
    $db.Execute($sql)
    Write-Host "  ✓ جدول الوحدات" -ForegroundColor Green
    
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Yellow
    
    # إدراج المستخدمين
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير', True);"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType, IsActive) VALUES ('accountant', '123456', 'المحاسب', 'محاسب', True);"
    $db.Execute($sql)
    
    Write-Host "  ✓ المستخدمين الأساسيين" -ForegroundColor Green
    
    # إدراج مشاريع تجريبية
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('مشروع الواحة السكني', 'الرياض', 'سكني', 5000000, 75, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus) VALUES ('برج التجارة', 'جدة', 'تجاري', ********, 45, 'قيد التنفيذ');"
    $db.Execute($sql)
    
    Write-Host "  ✓ المشاريع التجريبية" -ForegroundColor Green
    
    # إدراج عملاء تجريبيين
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType) VALUES ('أحمد محمد السعيد', '011-4567890', '0501234567', '<EMAIL>', 'فرد');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType) VALUES ('شركة الخليج للاستثمار', '012-9876543', '0559876543', '<EMAIL>', 'شركة');"
    $db.Execute($sql)
    
    Write-Host "  ✓ العملاء التجريبيين" -ForegroundColor Green
    
    # إدراج وحدات تجريبية
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-101', 'شقة', 120, 450000, 'متاح');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-102', 'شقة', 140, 520000, 'محجوز');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (2, 'OF-301', 'مكتب', 80, 320000, 'متاح');"
    $db.Execute($sql)
    
    Write-Host "  ✓ الوحدات التجريبية" -ForegroundColor Green
    
    Write-Host "إنشاء النماذج التفاعلية..." -ForegroundColor Yellow
    
    # إنشاء نموذج تسجيل الدخول
    Write-Host "  إنشاء نموذج تسجيل الدخول..." -ForegroundColor Cyan
    
    $loginForm = $access.CreateForm()
    $loginForm.Caption = "تسجيل الدخول - نظام رافع للتطوير العقاري"
    $loginForm.NavigationButtons = $false
    $loginForm.RecordSelectors = $false
    $loginForm.DividingLines = $false
    $loginForm.Modal = $true
    $loginForm.PopUp = $true
    $loginForm.BorderStyle = 3  # Dialog
    $loginForm.Width = 6000
    $loginForm.Height = 4000
    
    # حفظ نموذج تسجيل الدخول
    $access.DoCmd.Save(2, "frmLogin")
    $access.DoCmd.Close(2, "frmLogin")
    
    Write-Host "    ✓ نموذج تسجيل الدخول" -ForegroundColor Green
    
    # إنشاء النموذج الرئيسي
    Write-Host "  إنشاء النموذج الرئيسي..." -ForegroundColor Cyan
    
    $mainForm = $access.CreateForm()
    $mainForm.Caption = "نظام رافع للتطوير العقاري - الواجهة الرئيسية"
    $mainForm.NavigationButtons = $false
    $mainForm.RecordSelectors = $false
    $mainForm.WindowState = 2  # Maximized
    
    # حفظ النموذج الرئيسي
    $access.DoCmd.Save(2, "frmMain")
    $access.DoCmd.Close(2, "frmMain")
    
    Write-Host "    ✓ النموذج الرئيسي" -ForegroundColor Green
    
    # إنشاء نموذج المشاريع
    Write-Host "  إنشاء نموذج المشاريع..." -ForegroundColor Cyan
    
    $projectsForm = $access.CreateForm("Projects")
    $projectsForm.Caption = "إدارة المشاريع - نظام رافع"
    $projectsForm.NavigationButtons = $true
    $projectsForm.RecordSelectors = $true
    
    # حفظ نموذج المشاريع
    $access.DoCmd.Save(2, "frmProjects")
    $access.DoCmd.Close(2, "frmProjects")
    
    Write-Host "    ✓ نموذج المشاريع" -ForegroundColor Green
    
    # إنشاء نموذج العملاء
    Write-Host "  إنشاء نموذج العملاء..." -ForegroundColor Cyan
    
    $customersForm = $access.CreateForm("Customers")
    $customersForm.Caption = "إدارة العملاء - نظام رافع"
    $customersForm.NavigationButtons = $true
    $customersForm.RecordSelectors = $true
    
    # حفظ نموذج العملاء
    $access.DoCmd.Save(2, "frmCustomers")
    $access.DoCmd.Close(2, "frmCustomers")
    
    Write-Host "    ✓ نموذج العملاء" -ForegroundColor Green
    
    # إنشاء نموذج الوحدات
    Write-Host "  إنشاء نموذج الوحدات..." -ForegroundColor Cyan
    
    $unitsForm = $access.CreateForm("Units")
    $unitsForm.Caption = "إدارة الوحدات - نظام رافع"
    $unitsForm.NavigationButtons = $true
    $unitsForm.RecordSelectors = $true
    
    # حفظ نموذج الوحدات
    $access.DoCmd.Save(2, "frmUnits")
    $access.DoCmd.Close(2, "frmUnits")
    
    Write-Host "    ✓ نموذج الوحدات" -ForegroundColor Green
    
    Write-Host "إضافة كود VBA للنماذج..." -ForegroundColor Yellow
    
    # إنشاء وحدة VBA للنظام
    $module = $access.Modules.Add("RafeaSystemModule")
    
    $vbaCode = @'
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            LoginUser = True
            
            ' إغلاق نموذج تسجيل الدخول وفتح النموذج الرئيسي
            DoCmd.Close acForm, "frmLogin"
            DoCmd.OpenForm "frmMain"
            
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح!" & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType, vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة فتح نموذج المشاريع
Public Sub OpenProjectsForm()
    DoCmd.OpenForm "frmProjects"
End Sub

' وظيفة فتح نموذج العملاء
Public Sub OpenCustomersForm()
    DoCmd.OpenForm "frmCustomers"
End Sub

' وظيفة فتح نموذج الوحدات
Public Sub OpenUnitsForm()
    DoCmd.OpenForm "frmUnits"
End Sub

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    DoCmd.Close acForm, "frmMain"
    DoCmd.OpenForm "frmLogin"
    
    MsgBox "تم تسجيل الخروج بنجاح", vbInformation, "نظام رافع"
End Sub

' وظيفة عرض معلومات النظام
Public Sub ShowAbout()
    MsgBox "نظام رافع للتطوير العقاري" & vbCrLf & _
           "الإصدار 1.0" & vbCrLf & vbCrLf & _
           "نظام إداري ومحاسبي متكامل" & vbCrLf & _
           "لشركات التطوير العقاري" & vbCrLf & vbCrLf & _
           "© 2024 شركة رافع للتطوير العقاري", vbInformation, "حول النظام"
End Sub
'@
    
    $module.InsertText($vbaCode)
    Write-Host "  ✓ وحدة VBA الأساسية" -ForegroundColor Green
    
    # تعيين نموذج البداية
    Write-Host "تعيين إعدادات البداية..." -ForegroundColor Yellow
    
    # تعيين نموذج تسجيل الدخول كنموذج البداية
    $access.CurrentProject.Properties.Add("StartupForm", "frmLogin")
    $access.CurrentProject.Properties.Add("AllowBuiltinToolbars", $false)
    $access.CurrentProject.Properties.Add("AllowFullMenus", $false)
    $access.CurrentProject.Properties.Add("AllowBreakIntoCode", $false)
    $access.CurrentProject.Properties.Add("AllowSpecialKeys", $false)
    
    Write-Host "  ✓ تم تعيين نموذج البداية" -ForegroundColor Green
    
    # حفظ جميع التغييرات
    $access.DoCmd.Save()
    
    Write-Host ""
    Write-Host "✓ تم إنشاء نظام رافع مع النماذج التفاعلية بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $systemPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "النماذج المُنشأة:" -ForegroundColor Yellow
    Write-Host "  • frmLogin - نموذج تسجيل الدخول" -ForegroundColor Cyan
    Write-Host "  • frmMain - النموذج الرئيسي" -ForegroundColor Cyan
    Write-Host "  • frmProjects - نموذج المشاريع" -ForegroundColor Cyan
    Write-Host "  • frmCustomers - نموذج العملاء" -ForegroundColor Cyan
    Write-Host "  • frmUnits - نموذج الوحدات" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "اسم المستخدم: admin" -ForegroundColor Cyan
    Write-Host "كلمة المرور: admin123" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "عند فتح الملف سيظهر نموذج تسجيل الدخول تلقائياً!" -ForegroundColor Green
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "════════════════════════════════════════" -ForegroundColor Green
Write-Host "نظام رافع مع النماذج التفاعلية جاهز!" -ForegroundColor Yellow
Write-Host "════════════════════════════════════════" -ForegroundColor Green
