# دليل إنشاء النماذج التفاعلية يدوياً - نظام رافع
# Manual Forms Creation Guide - Rafea System

## 🎯 الهدف

تحويل قاعدة البيانات من مجرد جداول إلى نظام تفاعلي كامل مع:
- 🔐 صفحة تسجيل الدخول
- 🏠 الصفحة الرئيسية
- 📊 نماذج إدارة البيانات
- 🎛️ أزرار وقوائم تفاعلية

---

## 📋 الخطوات المطلوبة

### الخطوة 1: فتح قاعدة البيانات

1. **افتح ملف:** `RafeaSystem_Simple.accdb`
2. **ستظهر الجداول** في الجانب الأيسر
3. **لا تقلق** - سنضيف النماذج الآن!

### الخطوة 2: إنشاء نموذج تسجيل الدخول

#### أ. إنشاء النموذج:
1. **اذهب إلى تبويب Create**
2. **اضغط Form Design**
3. **سيفتح نموذج فارغ في وضع التصميم**

#### ب. تخصيص النموذج:
1. **انقر بالزر الأيمن على النموذج**
2. **اختر Properties**
3. **في تبويب Format:**
   - **Caption:** `تسجيل الدخول - نظام رافع`
   - **Modal:** `Yes`
   - **Pop Up:** `Yes`
   - **Border Style:** `Dialog`
   - **Navigation Buttons:** `No`
   - **Record Selectors:** `No`

#### ج. إضافة العناصر:
1. **من شريط الأدوات، اختر Label**
2. **أضف عنوان:** `نظام رافع للتطوير العقاري`
3. **أضف Label:** `اسم المستخدم:`
4. **أضف Text Box** بجانبه واسمه `txtUsername`
5. **أضف Label:** `كلمة المرور:`
6. **أضف Text Box** بجانبه واسمه `txtPassword`
7. **أضف Button** واسمه `btnLogin` والنص `تسجيل الدخول`

#### د. حفظ النموذج:
1. **اضغط Ctrl+S**
2. **اسم النموذج:** `frmLogin`
3. **اضغط OK**

### الخطوة 3: إنشاء النموذج الرئيسي

#### أ. إنشاء النموذج:
1. **Create > Form Design**
2. **نموذج فارغ جديد**

#### ب. تخصيص النموذج:
1. **Properties > Format:**
   - **Caption:** `نظام رافع - الواجهة الرئيسية`
   - **Window State:** `Maximized`
   - **Navigation Buttons:** `No`
   - **Record Selectors:** `No`

#### ج. إضافة العناصر:
1. **أضف Label كبير:** `مرحباً بك في نظام رافع`
2. **أضف أزرار للوحدات:**
   - **Button:** `إدارة المشاريع` (اسم: `btnProjects`)
   - **Button:** `إدارة العملاء` (اسم: `btnCustomers`)
   - **Button:** `إدارة الوحدات` (اسم: `btnUnits`)
   - **Button:** `لوحة التحكم` (اسم: `btnDashboard`)
   - **Button:** `تسجيل الخروج` (اسم: `btnLogout`)

#### د. حفظ النموذج:
1. **Ctrl+S**
2. **اسم النموذج:** `frmMain`

### الخطوة 4: إنشاء نماذج البيانات

#### أ. نموذج المشاريع:
1. **Create > Form Wizard**
2. **اختر جدول:** `Projects`
3. **اختر جميع الحقول**
4. **Layout:** `Columnar`
5. **اسم النموذج:** `frmProjects`

#### ب. نموذج العملاء:
1. **Form Wizard**
2. **جدول:** `Customers`
3. **جميع الحقول**
4. **اسم النموذج:** `frmCustomers`

#### ج. نموذج الوحدات:
1. **Form Wizard**
2. **جدول:** `Units`
3. **جميع الحقول**
4. **اسم النموذج:** `frmUnits`

### الخطوة 5: إضافة كود VBA

#### أ. فتح VBA Editor:
1. **اضغط Alt+F11**
2. **سيفتح Microsoft Visual Basic Editor**

#### ب. إنشاء وحدة جديدة:
1. **Insert > Module**
2. **سيظهر نافذة كود فارغة**

#### ج. نسخ الكود:
```vba
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            LoginUser = True
            
            ' إغلاق نموذج تسجيل الدخول وفتح النموذج الرئيسي
            DoCmd.Close acForm, "frmLogin"
            DoCmd.OpenForm "frmMain"
            
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح!", vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة فتح نموذج المشاريع
Public Sub OpenProjectsForm()
    DoCmd.OpenForm "frmProjects"
End Sub

' وظيفة فتح نموذج العملاء
Public Sub OpenCustomersForm()
    DoCmd.OpenForm "frmCustomers"
End Sub

' وظيفة فتح نموذج الوحدات
Public Sub OpenUnitsForm()
    DoCmd.OpenForm "frmUnits"
End Sub

' وظيفة عرض لوحة التحكم
Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم: " & CurrentUserFullName & vbCrLf
        msg = msg & "النوع: " & CurrentUserType & vbCrLf & vbCrLf
    End If
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    msg = msg & vbCrLf & "© 2024 شركة رافع للتطوير العقاري"
    
    MsgBox msg, vbInformation, "لوحة التحكم"
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    DoCmd.Close acForm, "frmMain"
    DoCmd.OpenForm "frmLogin"
    
    MsgBox "تم تسجيل الخروج بنجاح", vbInformation, "نظام رافع"
End Sub

' وظيفة بدء النظام
Public Sub StartSystem()
    DoCmd.OpenForm "frmLogin"
End Sub
```

#### د. حفظ الكود:
1. **اضغط Ctrl+S**
2. **اسم الوحدة:** `RafeaSystemModule`

### الخطوة 6: ربط الأزرار بالوظائف

#### أ. زر تسجيل الدخول:
1. **افتح نموذج `frmLogin` في وضع التصميم**
2. **انقر على زر `btnLogin`**
3. **Properties > Event > On Click**
4. **اختر `[Event Procedure]`**
5. **اضغط الثلاث نقاط `...`**
6. **أضف الكود:**
```vba
Private Sub btnLogin_Click()
    Dim username As String
    Dim password As String
    
    username = Me.txtUsername.Value
    password = Me.txtPassword.Value
    
    If LoginUser(username, password) Then
        ' تم تسجيل الدخول بنجاح
    End If
End Sub
```

#### ب. أزرار النموذج الرئيسي:
1. **افتح `frmMain` في وضع التصميم**
2. **لكل زر، أضف الكود المناسب:**

**زر المشاريع:**
```vba
Private Sub btnProjects_Click()
    OpenProjectsForm
End Sub
```

**زر العملاء:**
```vba
Private Sub btnCustomers_Click()
    OpenCustomersForm
End Sub
```

**زر الوحدات:**
```vba
Private Sub btnUnits_Click()
    OpenUnitsForm
End Sub
```

**زر لوحة التحكم:**
```vba
Private Sub btnDashboard_Click()
    ShowDashboard
End Sub
```

**زر تسجيل الخروج:**
```vba
Private Sub btnLogout_Click()
    LogoutUser
End Sub
```

### الخطوة 7: تعيين نموذج البداية

#### أ. إعدادات البداية:
1. **File > Options**
2. **Current Database**
3. **Display Form:** اختر `frmLogin`
4. **اضغط OK**

#### ب. إخفاء شريط التنقل:
1. **في نفس النافذة**
2. **Display Navigation Pane:** قم بإلغاء التحديد
3. **اضغط OK**

### الخطوة 8: اختبار النظام

#### أ. إغلاق وإعادة فتح:
1. **احفظ جميع التغييرات**
2. **أغلق Access**
3. **افتح الملف مرة أخرى**

#### ب. التحقق من النتيجة:
- ✅ **يجب أن يظهر نموذج تسجيل الدخول تلقائياً**
- ✅ **سجّل الدخول بـ:** `admin` / `admin123`
- ✅ **يجب أن يفتح النموذج الرئيسي**
- ✅ **جرب الأزرار المختلفة**

---

## 🎉 النتيجة النهائية

بعد اتباع هذه الخطوات، ستحصل على:

### 🔐 نموذج تسجيل الدخول:
- حقول اسم المستخدم وكلمة المرور
- زر تسجيل الدخول
- التحقق من صحة البيانات

### 🏠 النموذج الرئيسي:
- ترحيب بالمستخدم
- أزرار للوحدات المختلفة
- زر تسجيل الخروج

### 📊 نماذج البيانات:
- نموذج إدارة المشاريع
- نموذج إدارة العملاء
- نموذج إدارة الوحدات

### 🎛️ وظائف تفاعلية:
- تسجيل دخول آمن
- انتقال سلس بين النماذج
- لوحة تحكم بالإحصائيات
- تسجيل خروج آمن

---

## 🔧 نصائح إضافية

### تحسين المظهر:
1. **استخدم ألوان متناسقة**
2. **أضف شعار الشركة**
3. **نسّق الخطوط والأحجام**
4. **أضف أيقونات للأزرار**

### تحسين الوظائف:
1. **أضف رسائل تأكيد**
2. **تحقق من صحة البيانات**
3. **أضف مساعدة للمستخدم**
4. **اعمل نسخة احتياطية**

### الأمان:
1. **غيّر كلمات المرور الافتراضية**
2. **أضف مستخدمين جدد**
3. **حدد الصلاحيات**
4. **فعّل الحماية**

---

## 📞 المساعدة

إذا واجهت أي مشكلة:

1. **تأكد من حفظ جميع التغييرات**
2. **تحقق من أسماء النماذج والأزرار**
3. **راجع الكود للتأكد من عدم وجود أخطاء**
4. **جرب إغلاق وإعادة فتح Access**

**النظام الآن جاهز للاستخدام كتطبيق تفاعلي كامل! 🎊**
