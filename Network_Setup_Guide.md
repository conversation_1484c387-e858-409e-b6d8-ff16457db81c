# دليل إعداد النظام للعمل على الشبكة المحلية
# Network Setup Guide for Rafea Real Estate System

## 1. هيكل النظام على الشبكة

### تقسيم النظام:
- **Back-End (قاعدة البيانات)**: ملف واحد يوضع على الخادم أو الجهاز الرئيسي
- **Front-End (واجهة المستخدم)**: نسخ منفصلة على كل جهاز عميل

## 2. متطلبات الشبكة

### الأجهزة المطلوبة:
- جهاز خادم رئيسي (Server) أو جهاز كمبيوتر قوي
- أجهزة العملاء (Client Computers)
- راوتر Wi-Fi للشبكة المحلية
- كابلات شبكة (اختياري للاتصال السلكي)

### البرامج المطلوبة:
- Microsoft Access 2016 أو أحدث على جميع الأجهزة
- Windows 10 أو أحدث
- .NET Framework 4.7.2 أو أحدث

## 3. إعداد الخادم الرئيسي (Server Setup)

### الخطوة 1: إعداد مجلد مشترك
```
1. إنشاء مجلد على الخادم: C:\RafeaSystem\Database\
2. وضع ملف قاعدة البيانات: RafeaSystem_BE.accdb
3. مشاركة المجلد على الشبكة:
   - كليك يمين على المجلد → Properties
   - تبويب Sharing → Advanced Sharing
   - تفعيل "Share this folder"
   - تعيين اسم المشاركة: RafeaDB
   - تعيين الصلاحيات: Full Control للمستخدمين المصرحين
```

### الخطوة 2: إعداد الأمان
```
1. إنشاء مجموعة مستخدمين: RafeaUsers
2. إضافة المستخدمين المصرحين للمجموعة
3. تعيين صلاحيات المجلد:
   - RafeaUsers: Full Control
   - Everyone: Remove (إزالة)
```

### الخطوة 3: إعداد النسخ الاحتياطي التلقائي
```
1. إنشاء مجلد للنسخ الاحتياطية: C:\RafeaSystem\Backups\
2. إعداد مهمة مجدولة للنسخ الاحتياطي اليومي
3. استخدام الأمر: 
   copy "C:\RafeaSystem\Database\RafeaSystem_BE.accdb" "C:\RafeaSystem\Backups\RafeaSystem_BE_%date%.accdb"
```

## 4. إعداد أجهزة العملاء (Client Setup)

### الخطوة 1: تثبيت Front-End
```
1. إنشاء مجلد محلي: C:\RafeaSystem\
2. نسخ ملف Front-End: RafeaSystem_FE.accdb
3. إنشاء اختصار على سطح المكتب
```

### الخطوة 2: ربط قاعدة البيانات
```
1. فتح ملف Front-End
2. استخدام Linked Table Manager لربط الجداول
3. تحديد مسار الخادم: \\ServerName\RafeaDB\RafeaSystem_BE.accdb
4. أو استخدام IP Address: \\*************\RafeaDB\RafeaSystem_BE.accdb
```

## 5. ملف إعداد الاتصال (Connection Setup)

### إنشاء ملف تكوين (Config.ini):
```ini
[Database]
ServerPath=\\*************\RafeaDB\
DatabaseName=RafeaSystem_BE.accdb
BackupPath=\\*************\RafeaDB\Backups\
ConnectionTimeout=30

[Network]
ServerIP=*************
ServerName=RAFEA-SERVER
ShareName=RafeaDB

[Application]
AppName=نظام رافع للتطوير العقاري
Version=1.0
CompanyName=شركة رافع للتطوير العقاري
```

## 6. كود VBA لإدارة الاتصال

### وظيفة فحص الاتصال:
```vba
Public Function CheckNetworkConnection() As Boolean
    Dim ServerPath As String
    Dim TestFile As String
    
    On Error GoTo ErrorHandler
    
    ServerPath = GetConfigValue("Database", "ServerPath")
    TestFile = ServerPath & "connection_test.txt"
    
    ' محاولة إنشاء ملف اختبار
    Open TestFile For Output As #1
    Print #1, "Connection Test: " & Now()
    Close #1
    
    ' حذف ملف الاختبار
    Kill TestFile
    
    CheckNetworkConnection = True
    Exit Function
    
ErrorHandler:
    CheckNetworkConnection = False
    Close #1
End Function
```

### وظيفة قراءة ملف التكوين:
```vba
Public Function GetConfigValue(Section As String, Key As String) As String
    Dim ConfigFile As String
    Dim FileContent As String
    Dim Lines() As String
    Dim i As Integer
    Dim CurrentSection As String
    
    ConfigFile = CurrentProject.Path & "\Config.ini"
    
    If Dir(ConfigFile) = "" Then
        GetConfigValue = ""
        Exit Function
    End If
    
    ' قراءة محتوى الملف
    Open ConfigFile For Input As #1
    FileContent = Input$(LOF(1), 1)
    Close #1
    
    Lines = Split(FileContent, vbCrLf)
    
    For i = 0 To UBound(Lines)
        If Left(Lines(i), 1) = "[" And Right(Lines(i), 1) = "]" Then
            CurrentSection = Mid(Lines(i), 2, Len(Lines(i)) - 2)
        ElseIf CurrentSection = Section And InStr(Lines(i), "=") > 0 Then
            If Left(Lines(i), Len(Key) + 1) = Key & "=" Then
                GetConfigValue = Mid(Lines(i), Len(Key) + 2)
                Exit Function
            End If
        End If
    Next i
    
    GetConfigValue = ""
End Function
```

## 7. إعداد الأمان والصلاحيات

### مستويات الأمان:
1. **أمان الشبكة**: تشفير Wi-Fi (WPA3)
2. **أمان المجلد**: صلاحيات Windows
3. **أمان التطبيق**: نظام المستخدمين في Access
4. **أمان البيانات**: النسخ الاحتياطي المنتظم

### إعداد كلمات المرور:
```vba
' تشفير قاعدة البيانات
Public Sub EncryptDatabase()
    Dim db As DAO.Database
    Set db = CurrentDb
    
    ' تعيين كلمة مرور لقاعدة البيانات
    db.NewPassword "", "RafeaSystem2024!"
    
    Set db = Nothing
End Sub
```

## 8. مراقبة الأداء والصيانة

### مراقبة الاتصالات:
```vba
Public Sub LogConnectionStatus()
    Dim LogFile As String
    Dim LogEntry As String
    
    LogFile = CurrentProject.Path & "\ConnectionLog.txt"
    LogEntry = Now() & " - User: " & CurrentUserName & " - Status: Connected"
    
    Open LogFile For Append As #1
    Print #1, LogEntry
    Close #1
End Sub
```

### تنظيف قاعدة البيانات:
```vba
Public Sub CompactDatabase()
    Dim SourceDB As String
    Dim TempDB As String
    
    SourceDB = GetConfigValue("Database", "ServerPath") & GetConfigValue("Database", "DatabaseName")
    TempDB = GetConfigValue("Database", "ServerPath") & "Temp_" & GetConfigValue("Database", "DatabaseName")
    
    ' ضغط وإصلاح قاعدة البيانات
    DBEngine.CompactDatabase SourceDB, TempDB
    
    ' استبدال الملف الأصلي
    Kill SourceDB
    Name TempDB As SourceDB
End Sub
```

## 9. استكشاف الأخطاء وحلها

### المشاكل الشائعة والحلول:

1. **عدم القدرة على الاتصال بالخادم**
   - فحص اتصال الشبكة
   - التأكد من تشغيل الخادم
   - فحص صلاحيات المجلد المشترك

2. **بطء في الأداء**
   - ضغط قاعدة البيانات
   - تحسين الاستعلامات
   - زيادة ذاكرة الخادم

3. **تعارض في البيانات**
   - استخدام Record Locking
   - تحديث البيانات بانتظام
   - تدريب المستخدمين

## 10. نصائح للأداء الأمثل

1. **تحسين الشبكة**:
   - استخدام كابلات Gigabit Ethernet للخادم
   - تحديث راوتر Wi-Fi إلى معيار Wi-Fi 6
   - تقليل عدد الأجهزة على نفس الشبكة

2. **تحسين قاعدة البيانات**:
   - إنشاء فهارس للحقول المستخدمة في البحث
   - تقسيم الجداول الكبيرة
   - أرشفة البيانات القديمة

3. **تحسين التطبيق**:
   - تحميل البيانات عند الحاجة فقط
   - استخدام النماذج المحدودة
   - تقليل عدد الحقول المعروضة

## 11. خطة النسخ الاحتياطي

### النسخ الاحتياطي اليومي:
- نسخة تلقائية كل يوم في الساعة 11:00 مساءً
- الاحتفاظ بـ 30 نسخة يومية
- حذف النسخ الأقدم من شهر تلقائياً

### النسخ الاحتياطي الأسبوعي:
- نسخة كاملة كل يوم جمعة
- الاحتفاظ بـ 12 نسخة أسبوعية
- تخزين على قرص خارجي

### النسخ الاحتياطي الشهري:
- نسخة أرشيفية في نهاية كل شهر
- الاحتفاظ بـ 12 نسخة شهرية
- تخزين في موقع منفصل

## 12. دليل المستخدم السريع

### للمستخدم العادي:
1. تشغيل التطبيق من اختصار سطح المكتب
2. إدخال اسم المستخدم وكلمة المرور
3. استخدام النظام بشكل طبيعي
4. إغلاق التطبيق عند الانتهاء

### للمدير:
1. مراقبة حالة الخادم يومياً
2. فحص سجلات الأخطاء أسبوعياً
3. تنفيذ النسخ الاحتياطي اليدوي عند الحاجة
4. تحديث صلاحيات المستخدمين حسب الحاجة
