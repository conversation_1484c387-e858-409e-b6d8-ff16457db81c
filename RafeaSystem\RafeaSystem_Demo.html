<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام رافع للتطوير العقاري - عرض تجريبي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .login-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        .login-form {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-group label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-group input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            width: 200px;
        }
        
        .btn {
            padding: 12px 25px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .dashboard {
            display: none;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .module-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .module-card .icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .module-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .module-card p {
            color: #7f8c8d;
            line-height: 1.5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            color: #7f8c8d;
            margin-top: 5px;
        }
        
        .user-info {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .alert {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid #c3e6cb;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }
        
        .close {
            position: absolute;
            top: 15px;
            left: 20px;
            font-size: 28px;
            cursor: pointer;
            color: #aaa;
        }
        
        .close:hover {
            color: #000;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #27ae60;
            font-weight: bold;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏢 نظام رافع للتطوير العقاري</h1>
            <p>نظام إداري ومحاسبي متكامل لشركات التطوير العقاري</p>
        </div>

        <!-- Login Section -->
        <div class="login-section" id="loginSection">
            <h2 style="text-align: center; margin-bottom: 20px; color: #2c3e50;">تسجيل الدخول</h2>
            <div class="login-form">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="username" value="admin" placeholder="أدخل اسم المستخدم">
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="password" value="admin123" placeholder="أدخل كلمة المرور">
                </div>
                <button class="btn" onclick="login()">تسجيل الدخول</button>
            </div>
            <div class="alert">
                <strong>معلومات تسجيل الدخول الافتراضية:</strong><br>
                اسم المستخدم: admin<br>
                كلمة المرور: admin123
            </div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard">
            <!-- User Info -->
            <div class="user-info">
                <h3>مرحباً، <span id="currentUser">مدير النظام</span></h3>
                <p>نوع المستخدم: <span id="userType">مدير</span> | التاريخ: <span id="currentDate"></span></p>
                <button class="btn" onclick="logout()" style="margin-top: 10px;">تسجيل الخروج</button>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">المشاريع النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">الوحدات المتاحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">89</div>
                    <div class="stat-label">العقود النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">2.5M</div>
                    <div class="stat-label">إجمالي المبيعات (ريال)</div>
                </div>
            </div>

            <!-- Modules -->
            <div class="dashboard">
                <div class="module-card" onclick="openModule('projects')">
                    <div class="icon">🏗️</div>
                    <h3>إدارة المشاريع</h3>
                    <p>إدارة المشاريع العقارية والوحدات السكنية مع تتبع نسبة الإنجاز</p>
                </div>

                <div class="module-card" onclick="openModule('sales')">
                    <div class="icon">💰</div>
                    <h3>إدارة المبيعات</h3>
                    <p>إدارة العملاء والعقود والحجوزات مع نظام المدفوعات</p>
                </div>

                <div class="module-card" onclick="openModule('contractors')">
                    <div class="icon">🔨</div>
                    <h3>إدارة المقاولين</h3>
                    <p>إدارة المقاولين والمستخلصات والمدفوعات المرحلية</p>
                </div>

                <div class="module-card" onclick="openModule('suppliers')">
                    <div class="icon">📦</div>
                    <h3>إدارة الموردين</h3>
                    <p>إدارة الموردين والفواتير وسجلات الدفع</p>
                </div>

                <div class="module-card" onclick="openModule('purchases')">
                    <div class="icon">🛒</div>
                    <h3>إدارة المشتريات</h3>
                    <p>متابعة طلبات الشراء والموافقات المتعلقة بالمشاريع</p>
                </div>

                <div class="module-card" onclick="openModule('maintenance')">
                    <div class="icon">🔧</div>
                    <h3>الصيانة والتشغيل</h3>
                    <p>تسجيل ومتابعة أعمال الصيانة والتكاليف التشغيلية</p>
                </div>

                <div class="module-card" onclick="openModule('tasks')">
                    <div class="icon">✅</div>
                    <h3>المهام اليومية</h3>
                    <p>تسجيل وتتبع المهام اليومية حسب المستخدم أو القسم</p>
                </div>

                <div class="module-card" onclick="openModule('reports')">
                    <div class="icon">📊</div>
                    <h3>التقارير</h3>
                    <p>تقارير قابلة للطباعة مع خيارات التصفية والبحث</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="moduleModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">عنوان الوحدة</h2>
            <div id="modalContent">
                <!-- Module content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Set current date
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('ar-SA');

        function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            if (username === 'admin' && password === 'admin123') {
                document.getElementById('loginSection').style.display = 'none';
                document.getElementById('dashboard').style.display = 'grid';
                
                // Show success message
                alert('تم تسجيل الدخول بنجاح!\nمرحباً بك في نظام رافع للتطوير العقاري');
            } else {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        }

        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                document.getElementById('loginSection').style.display = 'block';
                document.getElementById('dashboard').style.display = 'none';
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'admin123';
            }
        }

        function openModule(moduleName) {
            const modal = document.getElementById('moduleModal');
            const title = document.getElementById('modalTitle');
            const content = document.getElementById('modalContent');

            const modules = {
                'projects': {
                    title: '🏗️ إدارة المشاريع',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إضافة مشروع جديد</li>
                            <li>تعديل بيانات المشاريع</li>
                            <li>إدارة الوحدات السكنية</li>
                            <li>تتبع نسبة الإنجاز</li>
                            <li>إدارة التكاليف</li>
                            <li>تقارير المشاريع</li>
                        </ul>
                        <p><strong>المشاريع النشطة:</strong> 12 مشروع</p>
                        <p><strong>إجمالي الوحدات:</strong> 245 وحدة</p>
                    `
                },
                'sales': {
                    title: '💰 إدارة المبيعات',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إدارة العملاء</li>
                            <li>إنشاء العقود</li>
                            <li>تسجيل المدفوعات</li>
                            <li>متابعة الأقساط</li>
                            <li>إدارة الحجوزات</li>
                            <li>تقارير المبيعات</li>
                        </ul>
                        <p><strong>العملاء المسجلين:</strong> 156 عميل</p>
                        <p><strong>العقود النشطة:</strong> 89 عقد</p>
                    `
                },
                'contractors': {
                    title: '🔨 إدارة المقاولين',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إدارة بيانات المقاولين</li>
                            <li>إنشاء عقود المقاولين</li>
                            <li>إدارة المستخلصات</li>
                            <li>اعتماد المستخلصات</li>
                            <li>تسجيل المدفوعات</li>
                            <li>تقارير المقاولين</li>
                        </ul>
                        <p><strong>المقاولين النشطين:</strong> 23 مقاول</p>
                        <p><strong>المستخلصات المعلقة:</strong> 8 مستخلص</p>
                    `
                },
                'suppliers': {
                    title: '📦 إدارة الموردين',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إدارة بيانات الموردين</li>
                            <li>إدارة الفواتير</li>
                            <li>اعتماد الفواتير</li>
                            <li>تسجيل المدفوعات</li>
                            <li>متابعة المستحقات</li>
                            <li>تقارير الموردين</li>
                        </ul>
                        <p><strong>الموردين النشطين:</strong> 45 مورد</p>
                        <p><strong>الفواتير المعلقة:</strong> 12 فاتورة</p>
                    `
                },
                'purchases': {
                    title: '🛒 إدارة المشتريات',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إنشاء طلبات الشراء</li>
                            <li>اعتماد الطلبات</li>
                            <li>متابعة حالة الطلبات</li>
                            <li>ربط الطلبات بالمشاريع</li>
                            <li>تقارير المشتريات</li>
                        </ul>
                        <p><strong>طلبات الشراء النشطة:</strong> 15 طلب</p>
                        <p><strong>طلبات معلقة للاعتماد:</strong> 5 طلبات</p>
                    `
                },
                'maintenance': {
                    title: '🔧 الصيانة والتشغيل',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>تسجيل أعمال الصيانة</li>
                            <li>جدولة الصيانة الوقائية</li>
                            <li>إدارة التكاليف التشغيلية</li>
                            <li>متابعة حالة الأعمال</li>
                            <li>تقارير الصيانة</li>
                        </ul>
                        <p><strong>أعمال الصيانة النشطة:</strong> 8 أعمال</p>
                        <p><strong>الصيانة المجدولة:</strong> 12 عمل</p>
                    `
                },
                'tasks': {
                    title: '✅ المهام اليومية',
                    content: `
                        <h3>الوظائف المتاحة:</h3>
                        <ul class="feature-list">
                            <li>إنشاء مهام جديدة</li>
                            <li>تكليف المهام للموظفين</li>
                            <li>متابعة تقدم المهام</li>
                            <li>تحديد الأولويات</li>
                            <li>تقارير الأداء</li>
                        </ul>
                        <p><strong>مهامك النشطة:</strong> 6 مهام</p>
                        <p><strong>المهام المكتملة اليوم:</strong> 4 مهام</p>
                    `
                },
                'reports': {
                    title: '📊 التقارير',
                    content: `
                        <h3>التقارير المتاحة:</h3>
                        <ul class="feature-list">
                            <li>تقارير المشاريع والوحدات</li>
                            <li>تقارير المبيعات والعملاء</li>
                            <li>تقارير المقاولين والمستخلصات</li>
                            <li>تقارير الموردين والفواتير</li>
                            <li>التقارير المالية</li>
                            <li>التقارير الإحصائية</li>
                        </ul>
                        <p><strong>خيارات التصدير:</strong> PDF, Excel, طباعة</p>
                        <p><strong>التقارير المحفوظة:</strong> 25 تقرير</p>
                    `
                }
            };

            title.textContent = modules[moduleName].title;
            content.innerHTML = modules[moduleName].content;
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('moduleModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('moduleModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // Allow Enter key to login
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter' && document.getElementById('loginSection').style.display !== 'none') {
                login();
            }
        });
    </script>
</body>
</html>
