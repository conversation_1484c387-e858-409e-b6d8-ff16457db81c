# تشغيل نظام رافع في Access
Write-Host "========================================" -ForegroundColor Green
Write-Host "    تشغيل نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$currentPath = Get-Location
$frontendPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_FE.accdb"
$databasePath = Join-Path $currentPath "RafeaSystem\Database\RafeaSystem_BE.accdb"

Write-Host "المسار الحالي: $currentPath" -ForegroundColor Cyan

# التحقق من وجود قاعدة البيانات الخلفية
if (-not (Test-Path $databasePath)) {
    Write-Host "إنشاء قاعدة البيانات الخلفية..." -ForegroundColor Yellow
    
    try {
        $access = New-Object -ComObject Access.Application
        $access.Visible = $false
        
        $db = $access.DBEngine.CreateDatabase($databasePath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
        
        # إنشاء جدول المستخدمين
        $sql = "CREATE TABLE Users (UserID AUTOINCREMENT PRIMARY KEY, Username TEXT(50) NOT NULL, Password TEXT(255) NOT NULL, FullName TEXT(100) NOT NULL, UserType TEXT(20) NOT NULL DEFAULT 'مستخدم', IsActive YESNO DEFAULT Yes, CreatedDate DATETIME DEFAULT Now(), LastLogin DATETIME);"
        $db.Execute($sql)
        
        # إدراج المستخدم الافتراضي
        $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
        $db.Execute($sql)
        
        $db.Close()
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        
        Write-Host "✓ تم إنشاء قاعدة البيانات" -ForegroundColor Green
    }
    catch {
        Write-Host "خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
        return
    }
}

# إنشاء الواجهة الأمامية إذا لم تكن موجودة
if (-not (Test-Path $frontendPath)) {
    Write-Host "إنشاء الواجهة الأمامية..." -ForegroundColor Yellow
    
    try {
        $access = New-Object -ComObject Access.Application
        $access.Visible = $false
        
        # إنشاء قاعدة بيانات الواجهة الأمامية
        $frontendDB = $access.DBEngine.CreateDatabase($frontendPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
        
        # ربط جدول المستخدمين
        $linkedTable = $frontendDB.CreateTableDef("Users")
        $linkedTable.Connect = ";DATABASE=$databasePath"
        $linkedTable.SourceTableName = "Users"
        $frontendDB.TableDefs.Append($linkedTable)
        
        $frontendDB.Close()
        
        # فتح الواجهة الأمامية وإضافة كود VBA
        $access.OpenCurrentDatabase($frontendPath)
        
        # إنشاء وحدة VBA
        $module = $access.Modules.Add("LoginModule")
        
        $vbaCode = @'
Option Compare Database
Option Explicit

Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

Public Function TestLogin() As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType FROM Users WHERE Username = 'admin'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        CurrentUserID = rs!UserID
        CurrentUserName = rs!Username
        CurrentUserFullName = rs!FullName
        CurrentUserType = rs!UserType
        
        MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
               "المستخدم: " & CurrentUserFullName & vbCrLf & _
               "النوع: " & CurrentUserType & vbCrLf & vbCrLf & _
               "معلومات تسجيل الدخول:" & vbCrLf & _
               "اسم المستخدم: admin" & vbCrLf & _
               "كلمة المرور: admin123" & vbCrLf & vbCrLf & _
               "وحدات النظام المتاحة:" & vbCrLf & _
               "• إدارة المشاريع" & vbCrLf & _
               "• إدارة المبيعات" & vbCrLf & _
               "• إدارة المقاولين" & vbCrLf & _
               "• إدارة الموردين" & vbCrLf & _
               "• إدارة المشتريات" & vbCrLf & _
               "• الصيانة والتشغيل" & vbCrLf & _
               "• المهام اليومية" & vbCrLf & _
               "• التقارير", vbInformation, "نظام رافع للتطوير العقاري"
        
        TestLogin = True
    Else
        MsgBox "لم يتم العثور على المستخدم", vbExclamation
        TestLogin = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "خطأ: " & Err.Description, vbCritical
    TestLogin = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

Public Sub ShowSystemInfo()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    
    msg = "=== نظام رافع للتطوير العقاري ===" & vbCrLf & vbCrLf
    msg = msg & "الإصدار: 1.0" & vbCrLf
    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf
    msg = msg & "الوقت: " & Format(Time, "hh:nn AM/PM") & vbCrLf & vbCrLf
    
    msg = msg & "=== إحصائيات النظام ===" & vbCrLf
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Users")
    msg = msg & "المستخدمين المسجلين: " & rs!Total & vbCrLf
    rs.Close
    
    msg = msg & vbCrLf & "=== الوحدات المتاحة ===" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات السكنية" & vbCrLf
    msg = msg & "✓ إدارة العملاء والعقود والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ إدارة المشتريات وطلبات الشراء" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتكاليف التشغيلية" & vbCrLf
    msg = msg & "✓ إدارة المهام اليومية" & vbCrLf
    msg = msg & "✓ نظام التقارير الشامل" & vbCrLf
    msg = msg & "✓ نظام الصلاحيات المتقدم" & vbCrLf
    msg = msg & "✓ النسخ الاحتياطي التلقائي" & vbCrLf
    
    msg = msg & vbCrLf & "© 2024 شركة رافع للتطوير العقاري"
    
    MsgBox msg, vbInformation, "معلومات النظام"
    
    Set rs = Nothing
    Set db = Nothing
End Sub
'@
        
        $module.InsertText($vbaCode)
        
        # حفظ وإغلاق
        $access.DoCmd.Save()
        $access.CloseCurrentDatabase()
        $access.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        
        Write-Host "✓ تم إنشاء الواجهة الأمامية" -ForegroundColor Green
    }
    catch {
        Write-Host "خطأ في إنشاء الواجهة الأمامية: $($_.Exception.Message)" -ForegroundColor Red
        return
    }
}

# تشغيل النظام
Write-Host ""
Write-Host "تشغيل نظام رافع للتطوير العقاري..." -ForegroundColor Green
Write-Host ""
Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
Write-Host "اسم المستخدم: admin" -ForegroundColor Cyan
Write-Host "كلمة المرور: admin123" -ForegroundColor Cyan
Write-Host ""
Write-Host "سيتم فتح Microsoft Access الآن..." -ForegroundColor Green

try {
    Start-Process $frontendPath
    Write-Host "✓ تم تشغيل النظام بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "تعليمات الاستخدام:" -ForegroundColor Yellow
    Write-Host "1. انتظر حتى يفتح Microsoft Access" -ForegroundColor White
    Write-Host "2. اضغط Ctrl+G لفتح نافذة Immediate" -ForegroundColor White
    Write-Host "3. اكتب: TestLogin واضغط Enter" -ForegroundColor White
    Write-Host "4. أو اكتب: ShowSystemInfo واضغط Enter" -ForegroundColor White
}
catch {
    Write-Host "خطأ في تشغيل النظام: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
