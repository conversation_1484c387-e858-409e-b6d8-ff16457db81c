' كود نموذج إدارة المبيعات
' Sales Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "إدارة مبيعات الشقق"
    Me.RightToLeft = True
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث البيانات
    RefreshData
    
    ' تحديث الإحصائيات
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم
    Me.cmdAddCustomer.Enabled = CheckPermission("المبيعات", "CanAdd")
    Me.cmdEditCustomer.Enabled = CheckPermission("المبيعات", "CanEdit")
    Me.cmdDeleteCustomer.Enabled = CheckPermission("المبيعات", "CanDelete")
    Me.cmdAddContract.Enabled = CheckPermission("المبيعات", "CanAdd")
    Me.cmdEditContract.Enabled = CheckPermission("المبيعات", "CanEdit")
    Me.cmdDeleteContract.Enabled = CheckPermission("المبيعات", "CanDelete")
    Me.cmdAddPayment.Enabled = CheckPermission("المبيعات", "CanAdd")
    Me.cmdPrint.Enabled = CheckPermission("المبيعات", "CanPrint")
End Sub

Private Sub RefreshData()
    ' تحديث قائمة العملاء
    Me.lstCustomers.RowSource = "SELECT CustomerID, CustomerName, Phone, Mobile, CustomerType " & _
                                "FROM Customers ORDER BY CustomerName"
    Me.lstCustomers.Requery
    
    ' تحديث قائمة العقود
    Me.lstContracts.RowSource = "SELECT c.ContractID, cu.CustomerName, p.ProjectName, u.UnitNumber, " & _
                                "c.TotalAmount, c.ContractStatus " & _
                                "FROM (((Contracts c " & _
                                "INNER JOIN Customers cu ON c.CustomerID = cu.CustomerID) " & _
                                "INNER JOIN Units u ON c.UnitID = u.UnitID) " & _
                                "INNER JOIN Projects p ON u.ProjectID = p.ProjectID) " & _
                                "ORDER BY c.ContractDate DESC"
    Me.lstContracts.Requery
    
    ' تحديث قائمة الوحدات المتاحة
    Me.cboAvailableUnits.RowSource = "SELECT u.UnitID, p.ProjectName & ' - وحدة ' & u.UnitNumber AS UnitDisplay " & _
                                     "FROM Units u INNER JOIN Projects p ON u.ProjectID = p.ProjectID " & _
                                     "WHERE u.UnitStatus = 'متاح' ORDER BY p.ProjectName, u.UnitNumber"
    Me.cboAvailableUnits.Requery
End Sub

Private Sub UpdateStatistics()
    ' تحديث إحصائيات المبيعات
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إجمالي العملاء
    Me.lblTotalCustomers.Caption = DCount("*", "Customers")
    
    ' إجمالي العقود
    Me.lblTotalContracts.Caption = DCount("*", "Contracts")
    Me.lblActiveContracts.Caption = DCount("*", "Contracts", "ContractStatus = 'نشط'")
    
    ' إجمالي الوحدات
    Me.lblTotalUnits.Caption = DCount("*", "Units")
    Me.lblAvailableUnits.Caption = DCount("*", "Units", "UnitStatus = 'متاح'")
    Me.lblSoldUnits.Caption = DCount("*", "Units", "UnitStatus = 'مباع'")
    Me.lblReservedUnits.Caption = DCount("*", "Units", "UnitStatus = 'محجوز'")
    
    ' إجمالي قيمة المبيعات
    Set rs = db.OpenRecordset("SELECT Sum(TotalAmount) AS TotalSales FROM Contracts WHERE ContractStatus = 'نشط'")
    If Not IsNull(rs!TotalSales) Then
        Me.lblTotalSales.Caption = FormatCurrency(rs!TotalSales)
    Else
        Me.lblTotalSales.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي المدفوعات
    Set rs = db.OpenRecordset("SELECT Sum(PaymentAmount) AS TotalPayments FROM Payments WHERE PaymentStatus = 'مدفوع'")
    If Not IsNull(rs!TotalPayments) Then
        Me.lblTotalPayments.Caption = FormatCurrency(rs!TotalPayments)
    Else
        Me.lblTotalPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' المبلغ المتبقي
    Set rs = db.OpenRecordset("SELECT Sum(c.TotalAmount) - Nz(Sum(p.PaymentAmount), 0) AS RemainingAmount " & _
                              "FROM Contracts c LEFT JOIN Payments p ON c.ContractID = p.ContractID " & _
                              "WHERE c.ContractStatus = 'نشط' AND (p.PaymentStatus = 'مدفوع' OR p.PaymentStatus IS NULL)")
    If Not IsNull(rs!RemainingAmount) Then
        Me.lblRemainingAmount.Caption = FormatCurrency(rs!RemainingAmount)
    Else
        Me.lblRemainingAmount.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إدارة العملاء
Private Sub cmdAddCustomer_Click()
    If Not CheckPermission("المبيعات", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة عميل جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmCustomerDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditCustomer_Click()
    If Not CheckPermission("المبيعات", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل العملاء", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstCustomers) Then
        MsgBox "يرجى اختيار عميل للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmCustomerDetails", acNormal, , "CustomerID = " & Me.lstCustomers, acFormEdit
End Sub

Private Sub cmdDeleteCustomer_Click()
    If Not CheckPermission("المبيعات", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف العملاء", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstCustomers) Then
        MsgBox "يرجى اختيار عميل للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود عقود للعميل
    If DCount("*", "Contracts", "CustomerID = " & Me.lstCustomers) > 0 Then
        MsgBox "لا يمكن حذف هذا العميل لوجود عقود مرتبطة به", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا العميل؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        CurrentDb.Execute "DELETE FROM Customers WHERE CustomerID = " & Me.lstCustomers
        LogActivity "حذف", "Customers", Me.lstCustomers, "حذف عميل"
        RefreshData
        MsgBox "تم حذف العميل بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

' إدارة العقود
Private Sub cmdAddContract_Click()
    If Not CheckPermission("المبيعات", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة عقد جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditContract_Click()
    If Not CheckPermission("المبيعات", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل العقود", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContracts) Then
        MsgBox "يرجى اختيار عقد للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractDetails", acNormal, , "ContractID = " & Me.lstContracts, acFormEdit
End Sub

Private Sub cmdDeleteContract_Click()
    If Not CheckPermission("المبيعات", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف العقود", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContracts) Then
        MsgBox "يرجى اختيار عقد للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود مدفوعات للعقد
    If DCount("*", "Payments", "ContractID = " & Me.lstContracts) > 0 Then
        MsgBox "لا يمكن حذف هذا العقد لوجود مدفوعات مرتبطة به", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا العقد؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        ' إعادة تعيين حالة الوحدة إلى متاح
        Dim UnitID As Long
        UnitID = DLookup("UnitID", "Contracts", "ContractID = " & Me.lstContracts)
        CurrentDb.Execute "UPDATE Units SET UnitStatus = 'متاح' WHERE UnitID = " & UnitID
        
        ' حذف العقد
        CurrentDb.Execute "DELETE FROM Contracts WHERE ContractID = " & Me.lstContracts
        LogActivity "حذف", "Contracts", Me.lstContracts, "حذف عقد"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف العقد بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

' إدارة المدفوعات
Private Sub cmdAddPayment_Click()
    If Not CheckPermission("المبيعات", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة دفعة جديدة", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContracts) Then
        MsgBox "يرجى اختيار عقد أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmPaymentDetails", acNormal, , "ContractID = " & Me.lstContracts, acFormAdd
End Sub

Private Sub cmdViewPayments_Click()
    If IsNull(Me.lstContracts) Then
        MsgBox "يرجى اختيار عقد أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmPayments", acNormal, , "ContractID = " & Me.lstContracts
End Sub

' البحث والتصفية
Private Sub cmdSearchCustomers_Click()
    Dim SearchText As String
    SearchText = InputBox("أدخل نص البحث:", "البحث في العملاء")
    
    If Len(Trim(SearchText)) > 0 Then
        Me.lstCustomers.RowSource = "SELECT CustomerID, CustomerName, Phone, Mobile, CustomerType " & _
                                    "FROM Customers WHERE CustomerName LIKE '*" & SearchText & "*' OR " & _
                                    "Phone LIKE '*" & SearchText & "*' OR Mobile LIKE '*" & SearchText & "*' " & _
                                    "ORDER BY CustomerName"
        Me.lstCustomers.Requery
        
        If Me.lstCustomers.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshData
        End If
    End If
End Sub

Private Sub cmdSearchContracts_Click()
    Dim SearchText As String
    SearchText = InputBox("أدخل نص البحث:", "البحث في العقود")
    
    If Len(Trim(SearchText)) > 0 Then
        Me.lstContracts.RowSource = "SELECT c.ContractID, cu.CustomerName, p.ProjectName, u.UnitNumber, " & _
                                    "c.TotalAmount, c.ContractStatus " & _
                                    "FROM (((Contracts c " & _
                                    "INNER JOIN Customers cu ON c.CustomerID = cu.CustomerID) " & _
                                    "INNER JOIN Units u ON c.UnitID = u.UnitID) " & _
                                    "INNER JOIN Projects p ON u.ProjectID = p.ProjectID) " & _
                                    "WHERE cu.CustomerName LIKE '*" & SearchText & "*' OR " & _
                                    "p.ProjectName LIKE '*" & SearchText & "*' OR " & _
                                    "u.UnitNumber LIKE '*" & SearchText & "*' " & _
                                    "ORDER BY c.ContractDate DESC"
        Me.lstContracts.Requery
        
        If Me.lstContracts.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshData
        End If
    End If
End Sub

' التقارير والطباعة
Private Sub cmdPrint_Click()
    If Not CheckPermission("المبيعات", "CanPrint") Then
        MsgBox "ليس لديك صلاحية لطباعة التقارير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    ' فتح نموذج اختيار التقرير
    DoCmd.OpenForm "frmSalesReports", acDialog
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation, "تحديث"
End Sub

Private Sub lstCustomers_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstCustomers) Then
        cmdEditCustomer_Click
    End If
End Sub

Private Sub lstContracts_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstContracts) Then
        cmdEditContract_Click
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Sales", 0, "إغلاق نموذج إدارة المبيعات"
End Sub
