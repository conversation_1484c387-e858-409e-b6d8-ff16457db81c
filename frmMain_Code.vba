' كود النموذج الرئيسي ولوحة التحكم
' Main Form and Dashboard Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "نظام رافع للتطوير العقاري - " & CurrentUserFullName & " (" & CurrentUserType & ")"
    Me.WindowState = acWindowStateMaximized
    
    ' تعيين اتجاه النص من اليمين لليسار
    Me.RightToLeft = True
    
    ' تحديث لوحة التحكم
    UpdateDashboard
    
    ' تعيين الصلاحيات حسب نوع المستخدم
    SetUserPermissions
    
    ' عرض رسالة ترحيب
    Me.lblWelcome.Caption = "مرحباً " & CurrentUserFullName
    Me.lblCurrentDate.Caption = "التاريخ: " & FormatArabicDate(Date)
    Me.lblCurrentTime.Caption = "الوقت: " & Format(Time, "hh:nn AM/PM")
End Sub

Private Sub UpdateDashboard()
    ' تحديث إحصائيات لوحة التحكم
    On Error GoTo ErrorHandler
    
    ' إحصائيات المشاريع
    Me.lblTotalProjects.Caption = DCount("*", "Projects")
    Me.lblActiveProjects.Caption = DCount("*", "Projects", "ProjectStatus = 'قيد التنفيذ'")
    Me.lblCompletedProjects.Caption = DCount("*", "Projects", "ProjectStatus = 'مكتمل'")
    
    ' إحصائيات المبيعات
    Me.lblTotalUnits.Caption = DCount("*", "Units")
    Me.lblAvailableUnits.Caption = DCount("*", "Units", "UnitStatus = 'متاح'")
    Me.lblSoldUnits.Caption = DCount("*", "Units", "UnitStatus = 'مباع'")
    Me.lblReservedUnits.Caption = DCount("*", "Units", "UnitStatus = 'محجوز'")
    
    ' إحصائيات العملاء
    Me.lblTotalCustomers.Caption = DCount("*", "Customers")
    Me.lblActiveContracts.Caption = DCount("*", "Contracts", "ContractStatus = 'نشط'")
    
    ' إحصائيات المقاولين
    Me.lblTotalContractors.Caption = DCount("*", "Contractors", "IsActive = True")
    Me.lblPendingExtracts.Caption = DCount("*", "Extracts", "ExtractStatus = 'قيد المراجعة'")
    
    ' إحصائيات الموردين
    Me.lblTotalSuppliers.Caption = DCount("*", "Suppliers", "IsActive = True")
    Me.lblPendingInvoices.Caption = DCount("*", "Invoices", "InvoiceStatus = 'قيد المراجعة'")
    
    ' إحصائيات المهام
    Me.lblTotalTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID)
    Me.lblPendingTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID & " AND TaskStatus IN ('جديد', 'قيد التنفيذ')")
    
    ' المبالغ المالية
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إجمالي قيمة المشاريع
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS TotalProjectsCost FROM Projects")
    If Not IsNull(rs!TotalProjectsCost) Then
        Me.lblTotalProjectsCost.Caption = FormatCurrency(rs!TotalProjectsCost)
    Else
        Me.lblTotalProjectsCost.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي المبيعات
    Set rs = db.OpenRecordset("SELECT Sum(TotalAmount) AS TotalSales FROM Contracts WHERE ContractStatus = 'نشط'")
    If Not IsNull(rs!TotalSales) Then
        Me.lblTotalSales.Caption = FormatCurrency(rs!TotalSales)
    Else
        Me.lblTotalSales.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي المدفوعات المستلمة
    Set rs = db.OpenRecordset("SELECT Sum(PaymentAmount) AS TotalPayments FROM Payments WHERE PaymentStatus = 'مدفوع'")
    If Not IsNull(rs!TotalPayments) Then
        Me.lblTotalPayments.Caption = FormatCurrency(rs!TotalPayments)
    Else
        Me.lblTotalPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

Private Sub SetUserPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم
    Select Case CurrentUserType
        Case "مدير"
            ' المدير له جميع الصلاحيات
            EnableAllMenus True
            
        Case "محاسب"
            ' المحاسب له صلاحيات محدودة
            Me.cmdProjects.Enabled = CheckPermission("المشاريع", "CanView")
            Me.cmdSales.Enabled = CheckPermission("المبيعات", "CanView")
            Me.cmdContractors.Enabled = CheckPermission("المقاولين", "CanView")
            Me.cmdSuppliers.Enabled = CheckPermission("الموردين", "CanView")
            Me.cmdPurchases.Enabled = CheckPermission("المشتريات", "CanView")
            Me.cmdMaintenance.Enabled = False
            Me.cmdTasks.Enabled = CheckPermission("المهام", "CanView")
            Me.cmdReports.Enabled = CheckPermission("التقارير", "CanView")
            Me.cmdUsers.Enabled = False
            
        Case "مهندس"
            ' المهندس له صلاحيات فنية
            Me.cmdProjects.Enabled = CheckPermission("المشاريع", "CanView")
            Me.cmdSales.Enabled = False
            Me.cmdContractors.Enabled = CheckPermission("المقاولين", "CanView")
            Me.cmdSuppliers.Enabled = CheckPermission("الموردين", "CanView")
            Me.cmdPurchases.Enabled = CheckPermission("المشتريات", "CanView")
            Me.cmdMaintenance.Enabled = CheckPermission("الصيانة", "CanView")
            Me.cmdTasks.Enabled = CheckPermission("المهام", "CanView")
            Me.cmdReports.Enabled = CheckPermission("التقارير", "CanView")
            Me.cmdUsers.Enabled = False
            
        Case "مبيعات"
            ' موظف المبيعات له صلاحيات المبيعات فقط
            Me.cmdProjects.Enabled = CheckPermission("المشاريع", "CanView")
            Me.cmdSales.Enabled = CheckPermission("المبيعات", "CanView")
            Me.cmdContractors.Enabled = False
            Me.cmdSuppliers.Enabled = False
            Me.cmdPurchases.Enabled = False
            Me.cmdMaintenance.Enabled = False
            Me.cmdTasks.Enabled = CheckPermission("المهام", "CanView")
            Me.cmdReports.Enabled = CheckPermission("التقارير", "CanView")
            Me.cmdUsers.Enabled = False
            
        Case Else
            ' مستخدم عادي - صلاحيات محدودة جداً
            EnableAllMenus False
            Me.cmdTasks.Enabled = CheckPermission("المهام", "CanView")
    End Select
End Sub

Private Sub EnableAllMenus(Enable As Boolean)
    Me.cmdProjects.Enabled = Enable
    Me.cmdSales.Enabled = Enable
    Me.cmdContractors.Enabled = Enable
    Me.cmdSuppliers.Enabled = Enable
    Me.cmdPurchases.Enabled = Enable
    Me.cmdMaintenance.Enabled = Enable
    Me.cmdTasks.Enabled = Enable
    Me.cmdReports.Enabled = Enable
    Me.cmdUsers.Enabled = Enable
End Sub

' أحداث الأزرار
Private Sub cmdProjects_Click()
    If CheckPermission("المشاريع", "CanView") Then
        DoCmd.OpenForm "frmProjects"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdSales_Click()
    If CheckPermission("المبيعات", "CanView") Then
        DoCmd.OpenForm "frmSales"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdContractors_Click()
    If CheckPermission("المقاولين", "CanView") Then
        DoCmd.OpenForm "frmContractors"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdSuppliers_Click()
    If CheckPermission("الموردين", "CanView") Then
        DoCmd.OpenForm "frmSuppliers"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdPurchases_Click()
    If CheckPermission("المشتريات", "CanView") Then
        DoCmd.OpenForm "frmPurchases"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdMaintenance_Click()
    If CheckPermission("الصيانة", "CanView") Then
        DoCmd.OpenForm "frmMaintenance"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdTasks_Click()
    If CheckPermission("المهام", "CanView") Then
        DoCmd.OpenForm "frmTasks"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdReports_Click()
    If CheckPermission("التقارير", "CanView") Then
        DoCmd.OpenForm "frmReports"
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذا القسم", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdUsers_Click()
    If CurrentUserType = "مدير" Then
        DoCmd.OpenForm "frmUsers"
    Else
        MsgBox "هذا القسم متاح للمدير فقط", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdSettings_Click()
    If CurrentUserType = "مدير" Then
        DoCmd.OpenForm "frmSettings"
    Else
        MsgBox "هذا القسم متاح للمدير فقط", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdBackup_Click()
    If CurrentUserType = "مدير" Then
        If MsgBox("هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟", vbYesNo + vbQuestion, "نسخ احتياطي") = vbYes Then
            BackupDatabase
        End If
    Else
        MsgBox "هذه الوظيفة متاحة للمدير فقط", vbExclamation, "صلاحيات غير كافية"
    End If
End Sub

Private Sub cmdLogout_Click()
    If MsgBox("هل تريد تسجيل الخروج؟", vbYesNo + vbQuestion, "تسجيل الخروج") = vbYes Then
        LogoutUser
    End If
End Sub

Private Sub cmdRefresh_Click()
    UpdateDashboard
    MsgBox "تم تحديث البيانات", vbInformation, "تحديث"
End Sub

Private Sub Timer1_Timer()
    ' تحديث الوقت كل دقيقة
    Me.lblCurrentTime.Caption = "الوقت: " & Format(Time, "hh:nn AM/PM")
End Sub

Private Sub Form_Timer()
    ' تحديث لوحة التحكم كل 5 دقائق
    UpdateDashboard
End Sub

Private Sub Form_Unload(Cancel As Integer)
    ' تسجيل الخروج عند إغلاق النموذج
    LogoutUser
End Sub
