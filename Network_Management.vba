' وحدة إدارة الشبكة لنظام رافع للتطوير العقاري
' Network Management Module for Rafea Real Estate System

Option Compare Database
Option Explicit

' متغيرات الشبكة العامة
Public ServerPath As String
Public DatabasePath As String
Public BackupPath As String
Public IsNetworkConnected As Boolean
Public LastConnectionCheck As Date

' تهيئة إعدادات الشبكة
Public Sub InitializeNetworkSettings()
    On Error GoTo ErrorHandler
    
    ' قراءة إعدادات الشبكة من ملف التكوين
    ServerPath = GetConfigValue("Database", "ServerPath")
    DatabasePath = ServerPath & GetConfigValue("Database", "DatabaseName")
    BackupPath = GetConfigValue("Database", "BackupPath")
    
    ' فحص الاتصال الأولي
    IsNetworkConnected = CheckNetworkConnection()
    LastConnectionCheck = Now()
    
    ' تسجيل حالة الاتصال
    LogConnectionStatus("تهيئة", "تم تهيئة إعدادات الشبكة")
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في تهيئة إعدادات الشبكة: " & Err.Description, vbCritical, "خطأ شبكة"
    IsNetworkConnected = False
End Sub

' فحص الاتصال بالشبكة
Public Function CheckNetworkConnection() As Boolean
    On Error GoTo ErrorHandler
    
    Dim TestFile As String
    Dim FileHandle As Integer
    
    ' إنشاء اسم ملف اختبار فريد
    TestFile = ServerPath & "connection_test_" & Format(Now(), "yyyymmdd_hhnnss") & ".tmp"
    
    ' محاولة إنشاء ملف اختبار
    FileHandle = FreeFile
    Open TestFile For Output As FileHandle
    Print #FileHandle, "Connection Test: " & Now() & " - User: " & CurrentUserName
    Close FileHandle
    
    ' التحقق من وجود الملف
    If Dir(TestFile) <> "" Then
        ' حذف ملف الاختبار
        Kill TestFile
        CheckNetworkConnection = True
        LastConnectionCheck = Now()
    Else
        CheckNetworkConnection = False
    End If
    
    Exit Function
    
ErrorHandler:
    CheckNetworkConnection = False
    If FileHandle > 0 Then Close FileHandle
    
    ' محاولة حذف ملف الاختبار في حالة الخطأ
    On Error Resume Next
    If Dir(TestFile) <> "" Then Kill TestFile
    On Error GoTo 0
End Function

' قراءة قيمة من ملف التكوين
Public Function GetConfigValue(Section As String, Key As String) As String
    On Error GoTo ErrorHandler
    
    Dim ConfigFile As String
    Dim FileContent As String
    Dim Lines() As String
    Dim i As Integer
    Dim CurrentSection As String
    Dim LineContent As String
    
    ConfigFile = CurrentProject.Path & "\Config.ini"
    
    ' التحقق من وجود ملف التكوين
    If Dir(ConfigFile) = "" Then
        CreateDefaultConfigFile ConfigFile
    End If
    
    ' قراءة محتوى الملف
    Open ConfigFile For Input As #1
    FileContent = Input$(LOF(1), 1)
    Close #1
    
    Lines = Split(FileContent, vbCrLf)
    
    For i = 0 To UBound(Lines)
        LineContent = Trim(Lines(i))
        
        ' تجاهل الأسطر الفارغة والتعليقات
        If Len(LineContent) = 0 Or Left(LineContent, 1) = ";" Then
            GoTo NextLine
        End If
        
        ' فحص أقسام الملف
        If Left(LineContent, 1) = "[" And Right(LineContent, 1) = "]" Then
            CurrentSection = Mid(LineContent, 2, Len(LineContent) - 2)
        ElseIf CurrentSection = Section And InStr(LineContent, "=") > 0 Then
            If Left(LineContent, Len(Key) + 1) = Key & "=" Then
                GetConfigValue = Mid(LineContent, Len(Key) + 2)
                Exit Function
            End If
        End If
        
NextLine:
    Next i
    
    GetConfigValue = ""
    Exit Function
    
ErrorHandler:
    GetConfigValue = ""
    Close #1
End Function

' إنشاء ملف تكوين افتراضي
Private Sub CreateDefaultConfigFile(ConfigFile As String)
    On Error GoTo ErrorHandler
    
    Open ConfigFile For Output As #1
    
    Print #1, "; ملف تكوين نظام رافع للتطوير العقاري"
    Print #1, "; Rafea Real Estate System Configuration File"
    Print #1, ""
    Print #1, "[Database]"
    Print #1, "ServerPath=\\*************\RafeaDB\"
    Print #1, "DatabaseName=RafeaSystem_BE.accdb"
    Print #1, "BackupPath=\\*************\RafeaDB\Backups\"
    Print #1, "ConnectionTimeout=30"
    Print #1, ""
    Print #1, "[Network]"
    Print #1, "ServerIP=*************"
    Print #1, "ServerName=RAFEA-SERVER"
    Print #1, "ShareName=RafeaDB"
    Print #1, ""
    Print #1, "[Application]"
    Print #1, "AppName=نظام رافع للتطوير العقاري"
    Print #1, "Version=1.0"
    Print #1, "CompanyName=شركة رافع للتطوير العقاري"
    Print #1, ""
    Print #1, "[Backup]"
    Print #1, "AutoBackup=True"
    Print #1, "BackupTime=23:00"
    Print #1, "RetentionDays=30"
    
    Close #1
    
    Exit Sub
    
ErrorHandler:
    Close #1
End Sub

' تسجيل حالة الاتصال
Public Sub LogConnectionStatus(ActionType As String, Description As String)
    On Error GoTo ErrorHandler
    
    Dim LogFile As String
    Dim LogEntry As String
    
    LogFile = CurrentProject.Path & "\NetworkLog.txt"
    LogEntry = Format(Now(), "yyyy-mm-dd hh:nn:ss") & " | " & _
               CurrentUserName & " | " & _
               ActionType & " | " & _
               Description & " | " & _
               "IP: " & GetLocalIPAddress()
    
    Open LogFile For Append As #1
    Print #1, LogEntry
    Close #1
    
    Exit Sub
    
ErrorHandler:
    Close #1
End Sub

' الحصول على عنوان IP المحلي
Private Function GetLocalIPAddress() As String
    On Error GoTo ErrorHandler
    
    Dim objWMI As Object
    Dim colItems As Object
    Dim objItem As Object
    
    Set objWMI = GetObject("winmgmts:")
    Set colItems = objWMI.ExecQuery("SELECT * FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled = True")
    
    For Each objItem In colItems
        If Not IsNull(objItem.IPAddress) Then
            GetLocalIPAddress = objItem.IPAddress(0)
            Exit Function
        End If
    Next
    
    GetLocalIPAddress = "Unknown"
    Exit Function
    
ErrorHandler:
    GetLocalIPAddress = "Error"
End Function

' إعادة ربط الجداول
Public Function RelinkTables() As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim tdf As DAO.TableDef
    Dim NewConnectionString As String
    Dim TablesRelinked As Integer
    
    Set db = CurrentDb
    NewConnectionString = DatabasePath
    TablesRelinked = 0
    
    ' فحص الاتصال أولاً
    If Not CheckNetworkConnection() Then
        MsgBox "لا يمكن الاتصال بالخادم. يرجى التحقق من اتصال الشبكة.", vbCritical, "خطأ في الاتصال"
        RelinkTables = False
        Exit Function
    End If
    
    ' إعادة ربط كل جدول مرتبط
    For Each tdf In db.TableDefs
        If Len(tdf.Connect) > 0 Then
            tdf.Connect = ";DATABASE=" & NewConnectionString
            tdf.RefreshLink
            TablesRelinked = TablesRelinked + 1
        End If
    Next tdf
    
    ' تسجيل النشاط
    LogConnectionStatus "إعادة ربط", "تم إعادة ربط " & TablesRelinked & " جدول"
    
    RelinkTables = True
    MsgBox "تم إعادة ربط " & TablesRelinked & " جدول بنجاح", vbInformation, "إعادة الربط"
    
    Set tdf = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    RelinkTables = False
    MsgBox "خطأ في إعادة ربط الجداول: " & Err.Description, vbCritical, "خطأ"
    Set tdf = Nothing
    Set db = Nothing
End Function

' فحص دوري للاتصال
Public Sub PeriodicConnectionCheck()
    On Error GoTo ErrorHandler
    
    ' فحص الاتصال كل 5 دقائق
    If DateDiff("n", LastConnectionCheck, Now()) >= 5 Then
        Dim PreviousStatus As Boolean
        PreviousStatus = IsNetworkConnected
        
        IsNetworkConnected = CheckNetworkConnection()
        
        ' إذا تغيرت حالة الاتصال
        If PreviousStatus <> IsNetworkConnected Then
            If IsNetworkConnected Then
                LogConnectionStatus "استعادة الاتصال", "تم استعادة الاتصال بالخادم"
                MsgBox "تم استعادة الاتصال بالخادم", vbInformation, "حالة الشبكة"
            Else
                LogConnectionStatus "انقطاع الاتصال", "انقطع الاتصال بالخادم"
                MsgBox "انقطع الاتصال بالخادم. يرجى التحقق من الشبكة.", vbExclamation, "تحذير شبكة"
            End If
        End If
    End If
    
    Exit Sub
    
ErrorHandler:
    ' تجاهل أخطاء الفحص الدوري
End Sub

' إنشاء نسخة احتياطية عبر الشبكة
Public Function CreateNetworkBackup() As Boolean
    On Error GoTo ErrorHandler
    
    Dim SourceFile As String
    Dim BackupFile As String
    Dim BackupFileName As String
    
    ' التحقق من الاتصال
    If Not CheckNetworkConnection() Then
        MsgBox "لا يمكن إنشاء نسخة احتياطية. الخادم غير متاح.", vbCritical, "خطأ في النسخ الاحتياطي"
        CreateNetworkBackup = False
        Exit Function
    End If
    
    SourceFile = DatabasePath
    BackupFileName = "RafeaSystem_Backup_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".accdb"
    BackupFile = BackupPath & BackupFileName
    
    ' إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    If Dir(BackupPath, vbDirectory) = "" Then
        MkDir BackupPath
    End If
    
    ' نسخ الملف
    FileCopy SourceFile, BackupFile
    
    ' تسجيل النشاط
    LogConnectionStatus "نسخ احتياطي", "تم إنشاء نسخة احتياطية: " & BackupFileName
    
    CreateNetworkBackup = True
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح:" & vbCrLf & BackupFile, vbInformation, "نسخ احتياطي"
    
    Exit Function
    
ErrorHandler:
    CreateNetworkBackup = False
    MsgBox "خطأ في إنشاء النسخة الاحتياطية: " & Err.Description, vbCritical, "خطأ"
End Function

' تنظيف النسخ الاحتياطية القديمة
Public Sub CleanupOldBackups()
    On Error GoTo ErrorHandler
    
    Dim BackupFiles As String
    Dim FileDate As Date
    Dim RetentionDays As Integer
    Dim DeletedCount As Integer
    
    RetentionDays = Val(GetConfigValue("Backup", "RetentionDays"))
    If RetentionDays = 0 Then RetentionDays = 30
    
    DeletedCount = 0
    BackupFiles = Dir(BackupPath & "RafeaSystem_Backup_*.accdb")
    
    Do While BackupFiles <> ""
        FileDate = FileDateTime(BackupPath & BackupFiles)
        
        ' حذف الملفات الأقدم من فترة الاحتفاظ
        If DateDiff("d", FileDate, Now()) > RetentionDays Then
            Kill BackupPath & BackupFiles
            DeletedCount = DeletedCount + 1
        End If
        
        BackupFiles = Dir
    Loop
    
    If DeletedCount > 0 Then
        LogConnectionStatus "تنظيف", "تم حذف " & DeletedCount & " نسخة احتياطية قديمة"
    End If
    
    Exit Sub
    
ErrorHandler:
    ' تجاهل أخطاء التنظيف
End Sub

' فحص مساحة القرص على الخادم
Public Function CheckServerDiskSpace() As Long
    On Error GoTo ErrorHandler
    
    Dim fso As Object
    Dim Drive As Object
    Dim ServerDrive As String
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    ServerDrive = Left(ServerPath, 3) ' مثل C:\
    
    Set Drive = fso.GetDrive(ServerDrive)
    CheckServerDiskSpace = Drive.FreeSpace / 1024 / 1024 ' بالميجابايت
    
    ' تحذير إذا كانت المساحة أقل من 1 جيجابايت
    If CheckServerDiskSpace < 1024 Then
        MsgBox "تحذير: مساحة القرص على الخادم منخفضة (" & CheckServerDiskSpace & " ميجابايت)", _
               vbExclamation, "مساحة القرص"
        LogConnectionStatus "تحذير", "مساحة القرص منخفضة: " & CheckServerDiskSpace & " MB"
    End If
    
    Set Drive = Nothing
    Set fso = Nothing
    Exit Function
    
ErrorHandler:
    CheckServerDiskSpace = -1
End Function

' إعداد مراقبة الشبكة التلقائية
Public Sub SetupNetworkMonitoring()
    ' تفعيل مؤقت لفحص الاتصال كل 5 دقائق
    Application.OnTime Now + TimeValue("00:05:00"), "PeriodicConnectionCheck"
    
    ' تفعيل النسخ الاحتياطي التلقائي
    Dim BackupTime As String
    BackupTime = GetConfigValue("Backup", "BackupTime")
    If BackupTime <> "" Then
        Application.OnTime TimeValue(BackupTime), "AutoBackupRoutine"
    End If
    
    LogConnectionStatus "مراقبة", "تم تفعيل مراقبة الشبكة التلقائية"
End Sub

' روتين النسخ الاحتياطي التلقائي
Public Sub AutoBackupRoutine()
    If GetConfigValue("Backup", "AutoBackup") = "True" Then
        CreateNetworkBackup
        CleanupOldBackups
        
        ' جدولة النسخة الاحتياطية التالية
        Dim BackupTime As String
        BackupTime = GetConfigValue("Backup", "BackupTime")
        Application.OnTime Date + 1 + TimeValue(BackupTime), "AutoBackupRoutine"
    End If
End Sub
