@echo off
chcp 65001 >nul
title نظام رافع للتطوير العقاري - Rafea Real Estate System

:MAIN_MENU
cls
echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           نظام رافع للتطوير العقاري                      ██
echo ██           Rafea Real Estate System                         ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo ═══════════════════════════════════════════════════════════════
echo                        القائمة الرئيسية
echo                         Main Menu
echo ═══════════════════════════════════════════════════════════════
echo.
echo [1] إعداد النظام لأول مرة          Setup System (First Time)
echo [2] تشغيل النظام                  Run System
echo [3] إنشاء نسخة احتياطية           Create Backup
echo [4] استعادة نسخة احتياطية         Restore Backup
echo [5] فحص النظام                   System Check
echo [6] إعدادات الشبكة               Network Settings
echo [7] دليل المستخدم                User Manual
echo [8] خروج                         Exit
echo.
echo ═══════════════════════════════════════════════════════════════
echo.
set /p choice="اختر رقم الخيار / Choose option number: "

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto RUN
if "%choice%"=="3" goto BACKUP
if "%choice%"=="4" goto RESTORE
if "%choice%"=="5" goto CHECK
if "%choice%"=="6" goto NETWORK
if "%choice%"=="7" goto MANUAL
if "%choice%"=="8" goto EXIT

echo خيار غير صحيح! / Invalid option!
timeout /t 2 >nul
goto MAIN_MENU

:SETUP
cls
echo ═══════════════════════════════════════════════════════════════
echo                      إعداد النظام لأول مرة
echo                      First Time Setup
echo ═══════════════════════════════════════════════════════════════
echo.
echo سيتم الآن إعداد النظام لأول مرة...
echo Setting up the system for the first time...
echo.
pause

echo الخطوة 1: إنشاء هيكل المجلدات...
echo Step 1: Creating directory structure...
call Create_RafeaSystem.bat

echo.
echo الخطوة 2: إنشاء قاعدة البيانات...
echo Step 2: Creating database...
powershell -ExecutionPolicy Bypass -File "Create_Database.ps1"

echo.
echo الخطوة 3: إنشاء الواجهة الأمامية...
echo Step 3: Creating frontend application...
powershell -ExecutionPolicy Bypass -File "Create_Frontend.ps1"

echo.
echo ✓ تم إعداد النظام بنجاح!
echo ✓ System setup completed successfully!
echo.
echo يمكنك الآن تشغيل النظام من الخيار رقم 2
echo You can now run the system using option 2
echo.
pause
goto MAIN_MENU

:RUN
cls
echo ═══════════════════════════════════════════════════════════════
echo                        تشغيل النظام
echo                        Running System
echo ═══════════════════════════════════════════════════════════════
echo.

if not exist "RafeaSystem\RafeaSystem_FE.accdb" (
    echo ✗ لم يتم العثور على ملفات النظام!
    echo ✗ System files not found!
    echo.
    echo يرجى تشغيل إعداد النظام أولاً (الخيار رقم 1)
    echo Please run system setup first (option 1)
    echo.
    pause
    goto MAIN_MENU
)

echo تشغيل نظام رافع للتطوير العقاري...
echo Starting Rafea Real Estate System...
echo.
echo معلومات تسجيل الدخول الافتراضية:
echo Default login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin123
echo.
echo ⚠️  تحذير: يرجى تغيير كلمة المرور الافتراضية بعد تسجيل الدخول
echo ⚠️  Warning: Please change the default password after login
echo.
pause

start "" "RafeaSystem\RafeaSystem_FE.accdb"
echo.
echo تم تشغيل النظام!
echo System started!
echo.
pause
goto MAIN_MENU

:BACKUP
cls
echo ═══════════════════════════════════════════════════════════════
echo                      إنشاء نسخة احتياطية
echo                      Create Backup
echo ═══════════════════════════════════════════════════════════════
echo.

if not exist "RafeaSystem\Database\RafeaSystem_BE.accdb" (
    echo ✗ لم يتم العثور على قاعدة البيانات!
    echo ✗ Database not found!
    pause
    goto MAIN_MENU
)

set backup_name=RafeaSystem_Backup_%date:~-4,4%-%date:~-10,2%-%date:~-7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set backup_name=%backup_name: =0%

echo إنشاء نسخة احتياطية...
echo Creating backup...
echo.

copy "RafeaSystem\Database\RafeaSystem_BE.accdb" "RafeaSystem\Backups\%backup_name%.accdb"

if %errorlevel%==0 (
    echo ✓ تم إنشاء النسخة الاحتياطية بنجاح!
    echo ✓ Backup created successfully!
    echo.
    echo اسم الملف: %backup_name%.accdb
    echo File name: %backup_name%.accdb
    echo المسار: RafeaSystem\Backups\
    echo Path: RafeaSystem\Backups\
) else (
    echo ✗ فشل في إنشاء النسخة الاحتياطية!
    echo ✗ Failed to create backup!
)

echo.
pause
goto MAIN_MENU

:RESTORE
cls
echo ═══════════════════════════════════════════════════════════════
echo                     استعادة نسخة احتياطية
echo                     Restore Backup
echo ═══════════════════════════════════════════════════════════════
echo.

if not exist "RafeaSystem\Backups" (
    echo ✗ لم يتم العثور على مجلد النسخ الاحتياطية!
    echo ✗ Backup directory not found!
    pause
    goto MAIN_MENU
)

echo النسخ الاحتياطية المتاحة:
echo Available backups:
echo.
dir /b "RafeaSystem\Backups\*.accdb" 2>nul

if %errorlevel%==1 (
    echo لا توجد نسخ احتياطية متاحة
    echo No backups available
    pause
    goto MAIN_MENU
)

echo.
set /p backup_file="أدخل اسم ملف النسخة الاحتياطية / Enter backup filename: "

if not exist "RafeaSystem\Backups\%backup_file%" (
    echo ✗ الملف غير موجود!
    echo ✗ File not found!
    pause
    goto MAIN_MENU
)

echo.
echo ⚠️  تحذير: سيتم استبدال قاعدة البيانات الحالية!
echo ⚠️  Warning: Current database will be replaced!
echo.
set /p confirm="هل أنت متأكد؟ (y/n) / Are you sure? (y/n): "

if /i "%confirm%"=="y" (
    copy "RafeaSystem\Backups\%backup_file%" "RafeaSystem\Database\RafeaSystem_BE.accdb"
    echo ✓ تم استعادة النسخة الاحتياطية بنجاح!
    echo ✓ Backup restored successfully!
) else (
    echo تم إلغاء العملية
    echo Operation cancelled
)

echo.
pause
goto MAIN_MENU

:CHECK
cls
echo ═══════════════════════════════════════════════════════════════
echo                         فحص النظام
echo                        System Check
echo ═══════════════════════════════════════════════════════════════
echo.

echo فحص ملفات النظام...
echo Checking system files...
echo.

if exist "RafeaSystem" (
    echo ✓ مجلد النظام موجود
    echo ✓ System directory exists
) else (
    echo ✗ مجلد النظام غير موجود
    echo ✗ System directory missing
)

if exist "RafeaSystem\Database\RafeaSystem_BE.accdb" (
    echo ✓ قاعدة البيانات الخلفية موجودة
    echo ✓ Backend database exists
) else (
    echo ✗ قاعدة البيانات الخلفية غير موجودة
    echo ✗ Backend database missing
)

if exist "RafeaSystem\RafeaSystem_FE.accdb" (
    echo ✓ الواجهة الأمامية موجودة
    echo ✓ Frontend application exists
) else (
    echo ✗ الواجهة الأمامية غير موجودة
    echo ✗ Frontend application missing
)

if exist "RafeaSystem\Config.ini" (
    echo ✓ ملف التكوين موجود
    echo ✓ Configuration file exists
) else (
    echo ✗ ملف التكوين غير موجود
    echo ✗ Configuration file missing
)

echo.
echo فحص Microsoft Access...
echo Checking Microsoft Access...

powershell -Command "try { $access = New-Object -ComObject Access.Application; $access.Quit(); Write-Host '✓ Microsoft Access متاح' -ForegroundColor Green; Write-Host '✓ Microsoft Access available' -ForegroundColor Green } catch { Write-Host '✗ Microsoft Access غير متاح' -ForegroundColor Red; Write-Host '✗ Microsoft Access not available' -ForegroundColor Red }"

echo.
pause
goto MAIN_MENU

:NETWORK
cls
echo ═══════════════════════════════════════════════════════════════
echo                       إعدادات الشبكة
echo                      Network Settings
echo ═══════════════════════════════════════════════════════════════
echo.
echo لإعداد النظام للعمل على الشبكة المحلية، يرجى مراجعة:
echo To setup the system for local network, please refer to:
echo.
echo 📁 Network_Setup_Guide.md
echo.
echo هذا الملف يحتوي على تعليمات مفصلة لإعداد:
echo This file contains detailed instructions for setting up:
echo.
echo • الخادم الرئيسي / Main Server
echo • أجهزة العملاء / Client Computers  
echo • مشاركة المجلدات / Folder Sharing
echo • الأمان والصلاحيات / Security & Permissions
echo • استكشاف الأخطاء / Troubleshooting
echo.

if exist "Network_Setup_Guide.md" (
    set /p open_guide="فتح دليل إعداد الشبكة؟ (y/n) / Open network setup guide? (y/n): "
    if /i "!open_guide!"=="y" (
        start "" "Network_Setup_Guide.md"
    )
)

echo.
pause
goto MAIN_MENU

:MANUAL
cls
echo ═══════════════════════════════════════════════════════════════
echo                        دليل المستخدم
echo                        User Manual
echo ═══════════════════════════════════════════════════════════════
echo.
echo أدلة النظام المتاحة:
echo Available system guides:
echo.
echo 📖 User_Manual.md - دليل المستخدم الشامل
echo 🔧 Testing_Documentation.md - دليل الاختبار والتوثيق الفني
echo 🌐 Network_Setup_Guide.md - دليل إعداد الشبكة
echo 📋 README.md - معلومات المشروع
echo.

echo [1] دليل المستخدم / User Manual
echo [2] التوثيق الفني / Technical Documentation  
echo [3] دليل الشبكة / Network Guide
echo [4] معلومات المشروع / Project Info
echo [5] العودة للقائمة الرئيسية / Back to Main Menu
echo.

set /p doc_choice="اختر الدليل / Choose guide: "

if "%doc_choice%"=="1" if exist "User_Manual.md" start "" "User_Manual.md"
if "%doc_choice%"=="2" if exist "Testing_Documentation.md" start "" "Testing_Documentation.md"
if "%doc_choice%"=="3" if exist "Network_Setup_Guide.md" start "" "Network_Setup_Guide.md"
if "%doc_choice%"=="4" if exist "README.md" start "" "README.md"
if "%doc_choice%"=="5" goto MAIN_MENU

echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo ════════════════════════════════════════════════════════════════
echo.
echo            شكراً لاستخدام نظام رافع للتطوير العقاري
echo            Thank you for using Rafea Real Estate System
echo.
echo                    © 2024 شركة رافع للتطوير العقاري
echo                © 2024 Rafea Real Estate Development
echo.
echo ════════════════════════════════════════════════════════════════
echo.
timeout /t 3 >nul
exit
