' كود نموذج إدارة المقاولين
' Contractors Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "إدارة المقاولين والمستخلصات"
    Me.RightToLeft = True
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث البيانات
    RefreshData
    
    ' تحديث الإحصائيات
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم
    Me.cmdAddContractor.Enabled = CheckPermission("المقاولين", "CanAdd")
    Me.cmdEditContractor.Enabled = CheckPermission("المقاولين", "CanEdit")
    Me.cmdDeleteContractor.Enabled = CheckPermission("المقاولين", "CanDelete")
    Me.cmdAddContract.Enabled = CheckPermission("المقاولين", "CanAdd")
    Me.cmdEditContract.Enabled = CheckPermission("المقاولين", "CanEdit")
    Me.cmdDeleteContract.Enabled = CheckPermission("المقاولين", "CanDelete")
    Me.cmdAddExtract.Enabled = CheckPermission("المقاولين", "CanAdd")
    Me.cmdApproveExtract.Enabled = CheckPermission("المقاولين", "CanEdit")
    Me.cmdAddPayment.Enabled = CheckPermission("المقاولين", "CanAdd")
    Me.cmdPrint.Enabled = CheckPermission("المقاولين", "CanPrint")
End Sub

Private Sub RefreshData()
    ' تحديث قائمة المقاولين
    Me.lstContractors.RowSource = "SELECT ContractorID, ContractorName, CompanyName, Phone, " & _
                                  "Mobile, Specialization, IsActive " & _
                                  "FROM Contractors ORDER BY ContractorName"
    Me.lstContractors.Requery
    
    ' تحديث قائمة عقود المقاولين
    Me.lstContractorContracts.RowSource = "SELECT cc.ContractorContractID, c.ContractorName, " & _
                                          "p.ProjectName, cc.ContractAmount, cc.ContractStatus " & _
                                          "FROM ((ContractorContracts cc " & _
                                          "INNER JOIN Contractors c ON cc.ContractorID = c.ContractorID) " & _
                                          "INNER JOIN Projects p ON cc.ProjectID = p.ProjectID) " & _
                                          "ORDER BY cc.ContractDate DESC"
    Me.lstContractorContracts.Requery
    
    ' تحديث قائمة المستخلصات
    Me.lstExtracts.RowSource = "SELECT e.ExtractID, e.ExtractNumber, c.ContractorName, " & _
                               "p.ProjectName, e.ExtractAmount, e.ExtractStatus " & _
                               "FROM (((Extracts e " & _
                               "INNER JOIN ContractorContracts cc ON e.ContractorContractID = cc.ContractorContractID) " & _
                               "INNER JOIN Contractors c ON cc.ContractorID = c.ContractorID) " & _
                               "INNER JOIN Projects p ON cc.ProjectID = p.ProjectID) " & _
                               "ORDER BY e.ExtractDate DESC"
    Me.lstExtracts.Requery
End Sub

Private Sub UpdateStatistics()
    ' تحديث إحصائيات المقاولين
    On Error GoTo ErrorHandler
    
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    
    ' إجمالي المقاولين
    Me.lblTotalContractors.Caption = DCount("*", "Contractors", "IsActive = True")
    
    ' إجمالي العقود
    Me.lblTotalContracts.Caption = DCount("*", "ContractorContracts")
    Me.lblActiveContracts.Caption = DCount("*", "ContractorContracts", "ContractStatus = 'نشط'")
    
    ' إجمالي المستخلصات
    Me.lblTotalExtracts.Caption = DCount("*", "Extracts")
    Me.lblPendingExtracts.Caption = DCount("*", "Extracts", "ExtractStatus = 'قيد المراجعة'")
    Me.lblApprovedExtracts.Caption = DCount("*", "Extracts", "ExtractStatus = 'معتمد'")
    
    ' إجمالي قيمة العقود
    Set rs = db.OpenRecordset("SELECT Sum(ContractAmount) AS TotalContracts FROM ContractorContracts WHERE ContractStatus = 'نشط'")
    If Not IsNull(rs!TotalContracts) Then
        Me.lblTotalContractsValue.Caption = FormatCurrency(rs!TotalContracts)
    Else
        Me.lblTotalContractsValue.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي قيمة المستخلصات المعتمدة
    Set rs = db.OpenRecordset("SELECT Sum(ExtractAmount) AS TotalExtracts FROM Extracts WHERE ExtractStatus = 'معتمد'")
    If Not IsNull(rs!TotalExtracts) Then
        Me.lblTotalExtractsValue.Caption = FormatCurrency(rs!TotalExtracts)
    Else
        Me.lblTotalExtractsValue.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' إجمالي المدفوعات
    Set rs = db.OpenRecordset("SELECT Sum(PaymentAmount) AS TotalPayments FROM ContractorPayments")
    If Not IsNull(rs!TotalPayments) Then
        Me.lblTotalPayments.Caption = FormatCurrency(rs!TotalPayments)
    Else
        Me.lblTotalPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    ' المبلغ المتبقي للدفع
    Set rs = db.OpenRecordset("SELECT Sum(e.ExtractAmount) - Nz(Sum(cp.PaymentAmount), 0) AS RemainingAmount " & _
                              "FROM Extracts e LEFT JOIN ContractorPayments cp ON e.ExtractID = cp.ExtractID " & _
                              "WHERE e.ExtractStatus = 'معتمد'")
    If Not IsNull(rs!RemainingAmount) Then
        Me.lblRemainingPayments.Caption = FormatCurrency(rs!RemainingAmount)
    Else
        Me.lblRemainingPayments.Caption = FormatCurrency(0)
    End If
    rs.Close
    
    Set rs = Nothing
    Set db = Nothing
    
    Exit Sub
    
ErrorHandler:
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' إدارة المقاولين
Private Sub cmdAddContractor_Click()
    If Not CheckPermission("المقاولين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة مقاول جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditContractor_Click()
    If Not CheckPermission("المقاولين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل المقاولين", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContractors) Then
        MsgBox "يرجى اختيار مقاول للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorDetails", acNormal, , "ContractorID = " & Me.lstContractors, acFormEdit
End Sub

Private Sub cmdDeleteContractor_Click()
    If Not CheckPermission("المقاولين", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف المقاولين", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContractors) Then
        MsgBox "يرجى اختيار مقاول للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود عقود للمقاول
    If DCount("*", "ContractorContracts", "ContractorID = " & Me.lstContractors) > 0 Then
        MsgBox "لا يمكن حذف هذا المقاول لوجود عقود مرتبطة به", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا المقاول؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        CurrentDb.Execute "DELETE FROM Contractors WHERE ContractorID = " & Me.lstContractors
        LogActivity "حذف", "Contractors", Me.lstContractors, "حذف مقاول"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف المقاول بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

' إدارة عقود المقاولين
Private Sub cmdAddContract_Click()
    If Not CheckPermission("المقاولين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة عقد جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorContractDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditContract_Click()
    If Not CheckPermission("المقاولين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل العقود", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContractorContracts) Then
        MsgBox "يرجى اختيار عقد للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorContractDetails", acNormal, , "ContractorContractID = " & Me.lstContractorContracts, acFormEdit
End Sub

Private Sub cmdDeleteContract_Click()
    If Not CheckPermission("المقاولين", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف العقود", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContractorContracts) Then
        MsgBox "يرجى اختيار عقد للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من وجود مستخلصات للعقد
    If DCount("*", "Extracts", "ContractorContractID = " & Me.lstContractorContracts) > 0 Then
        MsgBox "لا يمكن حذف هذا العقد لوجود مستخلصات مرتبطة به", vbExclamation, "لا يمكن الحذف"
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذا العقد؟", vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        CurrentDb.Execute "DELETE FROM ContractorContracts WHERE ContractorContractID = " & Me.lstContractorContracts
        LogActivity "حذف", "ContractorContracts", Me.lstContractorContracts, "حذف عقد مقاول"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف العقد بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

' إدارة المستخلصات
Private Sub cmdAddExtract_Click()
    If Not CheckPermission("المقاولين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة مستخلص جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstContractorContracts) Then
        MsgBox "يرجى اختيار عقد أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmExtractDetails", acNormal, , "ContractorContractID = " & Me.lstContractorContracts, acFormAdd
End Sub

Private Sub cmdViewExtracts_Click()
    If IsNull(Me.lstContractorContracts) Then
        MsgBox "يرجى اختيار عقد أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmExtracts", acNormal, , "ContractorContractID = " & Me.lstContractorContracts
End Sub

Private Sub cmdApproveExtract_Click()
    If Not CheckPermission("المقاولين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لاعتماد المستخلصات", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstExtracts) Then
        MsgBox "يرجى اختيار مستخلص للاعتماد", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من حالة المستخلص
    Dim ExtractStatus As String
    ExtractStatus = DLookup("ExtractStatus", "Extracts", "ExtractID = " & Me.lstExtracts)
    
    If ExtractStatus <> "قيد المراجعة" Then
        MsgBox "يمكن اعتماد المستخلصات التي في حالة 'قيد المراجعة' فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    If MsgBox("هل تريد اعتماد هذا المستخلص؟", vbYesNo + vbQuestion, "اعتماد المستخلص") = vbYes Then
        CurrentDb.Execute "UPDATE Extracts SET ExtractStatus = 'معتمد', ApprovedBy = " & CurrentUserID & _
                          ", ApprovalDate = Now() WHERE ExtractID = " & Me.lstExtracts
        
        LogActivity "اعتماد", "Extracts", Me.lstExtracts, "اعتماد مستخلص"
        RefreshData
        UpdateStatistics
        MsgBox "تم اعتماد المستخلص بنجاح", vbInformation, "تم الاعتماد"
    End If
End Sub

Private Sub cmdRejectExtract_Click()
    If Not CheckPermission("المقاولين", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لرفض المستخلصات", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstExtracts) Then
        MsgBox "يرجى اختيار مستخلص للرفض", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من حالة المستخلص
    Dim ExtractStatus As String
    ExtractStatus = DLookup("ExtractStatus", "Extracts", "ExtractID = " & Me.lstExtracts)
    
    If ExtractStatus <> "قيد المراجعة" Then
        MsgBox "يمكن رفض المستخلصات التي في حالة 'قيد المراجعة' فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    Dim RejectReason As String
    RejectReason = InputBox("أدخل سبب الرفض:", "رفض المستخلص")
    
    If Len(Trim(RejectReason)) > 0 Then
        CurrentDb.Execute "UPDATE Extracts SET ExtractStatus = 'مرفوض', ApprovedBy = " & CurrentUserID & _
                          ", ApprovalDate = Now(), Notes = '" & RejectReason & "' WHERE ExtractID = " & Me.lstExtracts
        
        LogActivity "رفض", "Extracts", Me.lstExtracts, "رفض مستخلص: " & RejectReason
        RefreshData
        UpdateStatistics
        MsgBox "تم رفض المستخلص", vbInformation, "تم الرفض"
    End If
End Sub

' إدارة المدفوعات
Private Sub cmdAddPayment_Click()
    If Not CheckPermission("المقاولين", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة دفعة جديدة", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstExtracts) Then
        MsgBox "يرجى اختيار مستخلص أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التحقق من أن المستخلص معتمد
    Dim ExtractStatus As String
    ExtractStatus = DLookup("ExtractStatus", "Extracts", "ExtractID = " & Me.lstExtracts)
    
    If ExtractStatus <> "معتمد" Then
        MsgBox "يمكن إضافة مدفوعات للمستخلصات المعتمدة فقط", vbExclamation, "حالة غير صحيحة"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorPaymentDetails", acNormal, , "ExtractID = " & Me.lstExtracts, acFormAdd
End Sub

Private Sub cmdViewPayments_Click()
    If IsNull(Me.lstExtracts) Then
        MsgBox "يرجى اختيار مستخلص أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    DoCmd.OpenForm "frmContractorPayments", acNormal, , "ExtractID = " & Me.lstExtracts
End Sub

' البحث والتصفية
Private Sub cmdSearchContractors_Click()
    Dim SearchText As String
    SearchText = InputBox("أدخل نص البحث:", "البحث في المقاولين")
    
    If Len(Trim(SearchText)) > 0 Then
        Me.lstContractors.RowSource = "SELECT ContractorID, ContractorName, CompanyName, Phone, " & _
                                      "Mobile, Specialization, IsActive " & _
                                      "FROM Contractors WHERE ContractorName LIKE '*" & SearchText & "*' OR " & _
                                      "CompanyName LIKE '*" & SearchText & "*' OR " & _
                                      "Specialization LIKE '*" & SearchText & "*' " & _
                                      "ORDER BY ContractorName"
        Me.lstContractors.Requery
        
        If Me.lstContractors.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshData
        End If
    End If
End Sub

' التقارير والطباعة
Private Sub cmdPrint_Click()
    If Not CheckPermission("المقاولين", "CanPrint") Then
        MsgBox "ليس لديك صلاحية لطباعة التقارير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    ' فتح نموذج اختيار التقرير
    DoCmd.OpenForm "frmContractorsReports", acDialog
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation, "تحديث"
End Sub

Private Sub lstContractors_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstContractors) Then
        cmdEditContractor_Click
    End If
End Sub

Private Sub lstContractorContracts_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstContractorContracts) Then
        cmdEditContract_Click
    End If
End Sub

Private Sub lstExtracts_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstExtracts) Then
        DoCmd.OpenForm "frmExtractDetails", acNormal, , "ExtractID = " & Me.lstExtracts, acFormReadOnly
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Contractors", 0, "إغلاق نموذج إدارة المقاولين"
End Sub
