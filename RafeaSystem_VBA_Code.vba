Option Compare Database
Option Explicit

' ═══════════════════════════════════════════════════════════════
'                    نظام رافع للتطوير العقاري
'                    كود VBA الأساسي للنظام
' ═══════════════════════════════════════════════════════════════

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' ═══════════════════════════════════════════════════════════════
'                        وظائف تسجيل الدخول
' ═══════════════════════════════════════════════════════════════

' وظيفة تسجيل الدخول الرئيسية
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        CurrentUserID = rs!UserID
        CurrentUserName = rs!Username
        CurrentUserFullName = rs!FullName
        CurrentUserType = rs!UserType
        
        LoginUser = True
        
        MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
               "تم تسجيل الدخول بنجاح في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
               "نوع المستخدم: " & CurrentUserType & vbCrLf & _
               "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & _
               "الوقت: " & Format(Time, "hh:nn AM/PM"), vbInformation, "نظام رافع"
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة", vbExclamation, "خطأ في تسجيل الدخول"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    MsgBox "تم تسجيل الخروج بنجاح." & vbCrLf & "شكراً لاستخدام نظام رافع للتطوير العقاري!", vbInformation, "تسجيل الخروج"
End Sub

' ═══════════════════════════════════════════════════════════════
'                        لوحة التحكم الرئيسية
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض لوحة التحكم الرئيسية
Public Sub ShowMainDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم الرئيسية" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم الحالي: " & CurrentUserFullName & vbCrLf
        msg = msg & "نوع المستخدم: " & CurrentUserType & vbCrLf
    End If
    
    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf
    msg = msg & "الوقت: " & Format(Time, "hh:nn AM/PM") & vbCrLf & vbCrLf
    
    msg = msg & "═══ إحصائيات النظام ═══" & vbCrLf
    
    ' إحصائيات المشاريع
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات العملاء
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات الوحدات
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'متاح'")
    msg = msg & "الوحدات المتاحة: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'محجوز'")
    msg = msg & "الوحدات المحجوزة: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'مباع'")
    msg = msg & "الوحدات المباعة: " & rs!Total & vbCrLf
    rs.Close
    
    ' إجمالي قيمة المشاريع
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close
    
    msg = msg & vbCrLf & "═══ وحدات النظام المتاحة ═══" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات السكنية" & vbCrLf
    msg = msg & "✓ إدارة العملاء والعقود والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ إدارة المشتريات وطلبات الشراء" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتكاليف التشغيلية" & vbCrLf
    msg = msg & "✓ إدارة المهام اليومية" & vbCrLf
    msg = msg & "✓ نظام التقارير الشامل" & vbCrLf
    msg = msg & "✓ نظام الصلاحيات المتقدم" & vbCrLf
    msg = msg & "✓ النسخ الاحتياطي التلقائي" & vbCrLf
    
    msg = msg & vbCrLf & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "لوحة التحكم - نظام رافع"
    
    Set rs = Nothing
    Set db = Nothing
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في عرض لوحة التحكم: " & Err.Description, vbCritical, "خطأ"
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف إدارة المشاريع
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض قائمة المشاريع
Public Sub ShowProjects()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Projects ORDER BY ProjectName")
    
    msg = "═══ قائمة المشاريع ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!ProjectName & vbCrLf
        msg = msg & "  الموقع: " & rs!ProjectLocation & vbCrLf
        msg = msg & "  النوع: " & rs!ProjectType & vbCrLf
        msg = msg & "  التكلفة: " & Format(rs!TotalCost, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  نسبة الإنجاز: " & rs!CompletionPercentage & "%" & vbCrLf
        msg = msg & "  الحالة: " & rs!ProjectStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "المشاريع - نظام رافع"
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف إدارة العملاء
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض قائمة العملاء
Public Sub ShowCustomers()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Customers ORDER BY CustomerName")
    
    msg = "═══ قائمة العملاء ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!CustomerName & vbCrLf
        msg = msg & "  النوع: " & rs!CustomerType & vbCrLf
        If Not IsNull(rs!Phone) Then msg = msg & "  الهاتف: " & rs!Phone & vbCrLf
        If Not IsNull(rs!Mobile) Then msg = msg & "  الجوال: " & rs!Mobile & vbCrLf
        If Not IsNull(rs!Email) Then msg = msg & "  البريد: " & rs!Email & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "العملاء - نظام رافع"
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف إدارة الوحدات
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض قائمة الوحدات
Public Sub ShowUnits()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT u.*, p.ProjectName FROM Units u LEFT JOIN Projects p ON u.ProjectID = p.ProjectID ORDER BY p.ProjectName, u.UnitNumber")
    
    msg = "═══ قائمة الوحدات ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• وحدة " & rs!UnitNumber
        If Not IsNull(rs!ProjectName) Then msg = msg & " - " & rs!ProjectName
        msg = msg & vbCrLf
        msg = msg & "  النوع: " & rs!UnitType & vbCrLf
        msg = msg & "  المساحة: " & rs!Area & " م²" & vbCrLf
        msg = msg & "  السعر: " & Format(rs!UnitPrice, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  الحالة: " & rs!UnitStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "الوحدات - نظام رافع"
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف النظام العامة
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض معلومات النظام
Public Sub ShowSystemInfo()
    Dim msg As String
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    نظام إداري ومحاسبي متكامل" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    msg = msg & "الإصدار: 1.0" & vbCrLf
    msg = msg & "تاريخ الإصدار: ديسمبر 2024" & vbCrLf
    msg = msg & "المطور: فريق التطوير - شركة رافع" & vbCrLf & vbCrLf
    
    msg = msg & "═══ الميزات الرئيسية ═══" & vbCrLf
    msg = msg & "✓ إدارة شاملة للمشاريع العقارية" & vbCrLf
    msg = msg & "✓ نظام مبيعات متكامل" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ نظام المشتريات" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتشغيل" & vbCrLf
    msg = msg & "✓ نظام المهام اليومية" & vbCrLf
    msg = msg & "✓ تقارير شاملة وقابلة للتخصيص" & vbCrLf
    msg = msg & "✓ نظام صلاحيات متقدم" & vbCrLf
    msg = msg & "✓ دعم الشبكة المحلية" & vbCrLf
    msg = msg & "✓ نسخ احتياطي تلقائي" & vbCrLf & vbCrLf
    
    msg = msg & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "معلومات النظام"
End Sub

' وظيفة الترحيب بالنظام
Public Sub WelcomeToRafea()
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
           "هذا النظام مُطور خصيصاً لإدارة شركات التطوير العقاري" & vbCrLf & vbCrLf & _
           "الوحدات المتاحة:" & vbCrLf & _
           "• إدارة المشاريع والوحدات السكنية" & vbCrLf & _
           "• إدارة العملاء والعقود والمبيعات" & vbCrLf & _
           "• إدارة المقاولين والمستخلصات" & vbCrLf & _
           "• إدارة الموردين والفواتير" & vbCrLf & _
           "• إدارة المشتريات وطلبات الشراء" & vbCrLf & _
           "• إدارة الصيانة والتكاليف التشغيلية" & vbCrLf & _
           "• إدارة المهام اليومية" & vbCrLf & _
           "• نظام التقارير الشامل" & vbCrLf & vbCrLf & _
           "معلومات تسجيل الدخول:" & vbCrLf & _
           "اسم المستخدم: admin" & vbCrLf & _
           "كلمة المرور: admin123", vbInformation, "نظام رافع للتطوير العقاري"
End Sub
