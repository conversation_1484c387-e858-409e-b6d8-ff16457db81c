' ═══════════════════════════════════════════════════════════════
'                    نظام رافع للتطوير العقاري
'                    أكواد VBA للنماذج التفاعلية
' ═══════════════════════════════════════════════════════════════

Option Compare Database
Option Explicit

' ═══════════════════════════════════════════════════════════════
'                        متغيرات النظام العامة
' ═══════════════════════════════════════════════════════════════

Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' ═══════════════════════════════════════════════════════════════
'                        وظائف النظام الأساسية
' ═══════════════════════════════════════════════════════════════

' وظيفة بدء النظام
Public Sub StartSystem()
    DoCmd.OpenForm "frmLogin"
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
           "معلومات تسجيل الدخول الافتراضية:" & vbCrLf & _
           "اسم المستخدم: admin" & vbCrLf & _
           "كلمة المرور: admin123", vbInformation, "نظام رافع"
End Sub

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            LoginUser = True
            
            ' إغلاق نموذج تسجيل الدخول وفتح النموذج الرئيسي
            DoCmd.Close acForm, "frmLogin"
            DoCmd.OpenForm "frmMain"
            
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح في نظام رافع!" & vbCrLf & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType & vbCrLf & _
                   "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & _
                   "الوقت: " & Format(Time, "hh:nn AM/PM"), vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط. يرجى الاتصال بالمدير.", vbExclamation, "خطأ"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة.", vbExclamation, "خطأ"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    Dim response As Integer
    
    response = MsgBox("هل تريد تسجيل الخروج من النظام؟", vbYesNo + vbQuestion, "تسجيل الخروج")
    
    If response = vbYes Then
        CurrentUserID = 0
        CurrentUserName = ""
        CurrentUserFullName = ""
        CurrentUserType = ""
        
        ' إغلاق جميع النماذج المفتوحة عدا نموذج تسجيل الدخول
        Dim frm As Form
        For Each frm In Forms
            If frm.Name <> "frmLogin" Then
                DoCmd.Close acForm, frm.Name
            End If
        Next frm
        
        DoCmd.OpenForm "frmLogin"
        
        MsgBox "تم تسجيل الخروج بنجاح." & vbCrLf & "شكراً لاستخدام نظام رافع!", vbInformation, "تسجيل الخروج"
    End If
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف فتح النماذج
' ═══════════════════════════════════════════════════════════════

' وظيفة فتح نموذج المشاريع
Public Sub OpenProjectsForm()
    DoCmd.OpenForm "frmProjects"
End Sub

' وظيفة فتح نموذج العملاء
Public Sub OpenCustomersForm()
    DoCmd.OpenForm "frmCustomers"
End Sub

' وظيفة فتح نموذج الوحدات
Public Sub OpenUnitsForm()
    DoCmd.OpenForm "frmUnits"
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف لوحة التحكم
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض لوحة التحكم الرئيسية
Public Sub ShowMainDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم الرئيسية" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    If CurrentUserFullName <> "" Then
        msg = msg & "المستخدم الحالي: " & CurrentUserFullName & vbCrLf
        msg = msg & "نوع المستخدم: " & CurrentUserType & vbCrLf
    End If
    
    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf
    msg = msg & "الوقت: " & Format(Time, "hh:nn AM/PM") & vbCrLf & vbCrLf
    
    msg = msg & "═══ إحصائيات النظام ═══" & vbCrLf
    
    ' إحصائيات المشاريع
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات العملاء
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات الوحدات
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'متاح'")
    msg = msg & "الوحدات المتاحة: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'محجوز'")
    msg = msg & "الوحدات المحجوزة: " & rs!Total & vbCrLf
    rs.Close
    
    ' إجمالي قيمة المشاريع
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close
    
    msg = msg & vbCrLf & "═══ النماذج المتاحة ═══" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات" & vbCrLf
    msg = msg & "✓ إدارة العملاء والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة الوحدات السكنية" & vbCrLf
    msg = msg & "✓ تقارير شاملة" & vbCrLf
    
    msg = msg & vbCrLf & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "لوحة التحكم - نظام رافع"
    
    Set rs = Nothing
    Set db = Nothing
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في عرض لوحة التحكم: " & Err.Description, vbCritical, "خطأ"
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف معلومات النظام
' ═══════════════════════════════════════════════════════════════

' وظيفة عرض معلومات النظام
Public Sub ShowAbout()
    Dim msg As String
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    نظام إداري ومحاسبي متكامل" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    msg = msg & "الإصدار: 1.0" & vbCrLf
    msg = msg & "تاريخ الإصدار: ديسمبر 2024" & vbCrLf
    msg = msg & "المطور: فريق التطوير - شركة رافع" & vbCrLf & vbCrLf
    
    msg = msg & "═══ النماذج المتاحة ═══" & vbCrLf
    msg = msg & "✓ نموذج تسجيل الدخول" & vbCrLf
    msg = msg & "✓ النموذج الرئيسي" & vbCrLf
    msg = msg & "✓ نموذج إدارة المشاريع" & vbCrLf
    msg = msg & "✓ نموذج إدارة العملاء" & vbCrLf
    msg = msg & "✓ نموذج إدارة الوحدات" & vbCrLf & vbCrLf
    
    msg = msg & "═══ الميزات الرئيسية ═══" & vbCrLf
    msg = msg & "✓ واجهة تفاعلية كاملة" & vbCrLf
    msg = msg & "✓ نظام تسجيل دخول آمن" & vbCrLf
    msg = msg & "✓ إدارة شاملة للبيانات" & vbCrLf
    msg = msg & "✓ تقارير وإحصائيات" & vbCrLf
    msg = msg & "✓ واجهة عربية كاملة" & vbCrLf & vbCrLf
    
    msg = msg & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "حول النظام"
End Sub

' وظيفة المساعدة السريعة
Public Sub ShowQuickHelp()
    Dim msg As String
    
    msg = "═══ مساعدة سريعة - نظام رافع ═══" & vbCrLf & vbCrLf
    
    msg = msg & "🔐 تسجيل الدخول:" & vbCrLf
    msg = msg & "اسم المستخدم: admin" & vbCrLf
    msg = msg & "كلمة المرور: admin123" & vbCrLf & vbCrLf
    
    msg = msg & "🎛️ الأوامر المتاحة:" & vbCrLf
    msg = msg & "StartSystem - بدء النظام" & vbCrLf
    msg = msg & "ShowMainDashboard - لوحة التحكم" & vbCrLf
    msg = msg & "OpenProjectsForm - نموذج المشاريع" & vbCrLf
    msg = msg & "OpenCustomersForm - نموذج العملاء" & vbCrLf
    msg = msg & "OpenUnitsForm - نموذج الوحدات" & vbCrLf
    msg = msg & "ShowAbout - حول النظام" & vbCrLf
    msg = msg & "LogoutUser - تسجيل الخروج" & vbCrLf & vbCrLf
    
    msg = msg & "📋 للاستخدام:" & vbCrLf
    msg = msg & "1. اضغط Ctrl+G لفتح Immediate Window" & vbCrLf
    msg = msg & "2. اكتب اسم الوظيفة واضغط Enter" & vbCrLf
    msg = msg & "3. أو استخدم الأزرار في النماذج" & vbCrLf & vbCrLf
    
    msg = msg & "📞 للدعم: <EMAIL>"
    
    MsgBox msg, vbInformation, "مساعدة سريعة"
End Sub

' ═══════════════════════════════════════════════════════════════
'                    أكواد أحداث النماذج
' ═══════════════════════════════════════════════════════════════

' كود زر تسجيل الدخول في نموذج frmLogin
' يُضاف في Event Procedure للزر btnLogin
Public Sub btnLogin_Click()
    Dim username As String
    Dim password As String
    
    ' الحصول على القيم من حقول النموذج
    username = Forms("frmLogin")("txtUsername").Value
    password = Forms("frmLogin")("txtPassword").Value
    
    ' التحقق من وجود القيم
    If Len(username) = 0 Then
        MsgBox "يرجى إدخال اسم المستخدم", vbExclamation, "خطأ"
        Forms("frmLogin")("txtUsername").SetFocus
        Exit Sub
    End If
    
    If Len(password) = 0 Then
        MsgBox "يرجى إدخال كلمة المرور", vbExclamation, "خطأ"
        Forms("frmLogin")("txtPassword").SetFocus
        Exit Sub
    End If
    
    ' محاولة تسجيل الدخول
    If LoginUser(username, password) Then
        ' تم تسجيل الدخول بنجاح - سيتم فتح النموذج الرئيسي تلقائياً
    Else
        ' فشل تسجيل الدخول - مسح الحقول
        Forms("frmLogin")("txtUsername").Value = ""
        Forms("frmLogin")("txtPassword").Value = ""
        Forms("frmLogin")("txtUsername").SetFocus
    End If
End Sub

' أكواد أزرار النموذج الرئيسي frmMain
' تُضاف في Event Procedures للأزرار المختلفة

Public Sub btnProjects_Click()
    OpenProjectsForm
End Sub

Public Sub btnCustomers_Click()
    OpenCustomersForm
End Sub

Public Sub btnUnits_Click()
    OpenUnitsForm
End Sub

Public Sub btnDashboard_Click()
    ShowMainDashboard
End Sub

Public Sub btnLogout_Click()
    LogoutUser
End Sub

Public Sub btnAbout_Click()
    ShowAbout
End Sub

Public Sub btnHelp_Click()
    ShowQuickHelp
End Sub

' ═══════════════════════════════════════════════════════════════
'                        وظائف مساعدة
' ═══════════════════════════════════════════════════════════════

' وظيفة التحقق من صلاحيات المستخدم
Public Function CheckUserPermission(RequiredType As String) As Boolean
    If CurrentUserType = "مدير" Then
        CheckUserPermission = True
    ElseIf CurrentUserType = RequiredType Then
        CheckUserPermission = True
    Else
        MsgBox "ليس لديك صلاحية للوصول إلى هذه الوظيفة." & vbCrLf & _
               "نوع المستخدم المطلوب: " & RequiredType, vbExclamation, "صلاحيات غير كافية"
        CheckUserPermission = False
    End If
End Function

' وظيفة تسجيل العمليات
Public Sub LogActivity(Activity As String)
    Dim db As DAO.Database
    Dim SQL As String
    
    On Error Resume Next
    
    Set db = CurrentDb
    SQL = "INSERT INTO ActivityLog (UserID, Activity, ActivityDate) VALUES (" & CurrentUserID & ", '" & Activity & "', Now())"
    db.Execute SQL
    
    Set db = Nothing
End Sub

' ═══════════════════════════════════════════════════════════════
'                        نهاية الكود
' ═══════════════════════════════════════════════════════════════
