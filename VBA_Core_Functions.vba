' ملف الوظائف الأساسية لنظام رافع للتطوير العقاري
' Core Functions for Rafea Real Estate Development System

Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String
Public DatabasePath As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    ' التحقق من بيانات المستخدم
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive " & _
          "FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            ' حفظ بيانات المستخدم الحالي
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            ' تحديث آخر تسجيل دخول
            db.Execute "UPDATE Users SET LastLogin = Now() WHERE UserID = " & CurrentUserID
            
            ' تسجيل النشاط
            LogActivity "تسجيل دخول", "Users", CurrentUserID, "تسجيل دخول المستخدم: " & CurrentUserFullName
            
            LoginUser = True
        Else
            MsgBox "هذا المستخدم غير نشط. يرجى الاتصال بالمدير.", vbExclamation, "خطأ في تسجيل الدخول"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة.", vbExclamation, "خطأ في تسجيل الدخول"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    ' تسجيل النشاط
    LogActivity "تسجيل خروج", "Users", CurrentUserID, "تسجيل خروج المستخدم: " & CurrentUserFullName
    
    ' مسح بيانات المستخدم الحالي
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    ' إغلاق جميع النماذج المفتوحة
    CloseAllForms
    
    ' فتح نموذج تسجيل الدخول
    DoCmd.OpenForm "frmLogin"
End Sub

' وظيفة التحقق من الصلاحيات
Public Function CheckPermission(ModuleName As String, PermissionType As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    ' المدير له جميع الصلاحيات
    If CurrentUserType = "مدير" Then
        CheckPermission = True
        Exit Function
    End If
    
    Set db = CurrentDb
    
    SQL = "SELECT " & PermissionType & " FROM Permissions " & _
          "WHERE UserID = " & CurrentUserID & " AND ModuleName = '" & ModuleName & "'"
    
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        CheckPermission = rs(PermissionType)
    Else
        CheckPermission = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    CheckPermission = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تسجيل النشاطات
Public Sub LogActivity(ActivityType As String, TableName As String, RecordID As Long, Description As String)
    Dim db As DAO.Database
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    SQL = "INSERT INTO ActivityLog (UserID, ActivityType, TableName, RecordID, ActivityDescription) " & _
          "VALUES (" & CurrentUserID & ", '" & ActivityType & "', '" & TableName & "', " & _
          RecordID & ", '" & Description & "')"
    
    db.Execute SQL
    
    Set db = Nothing
    Exit Sub
    
ErrorHandler:
    Set db = Nothing
End Sub

' وظيفة إغلاق جميع النماذج
Private Sub CloseAllForms()
    Dim frm As Form
    Dim i As Integer
    
    For i = Forms.Count - 1 To 0 Step -1
        Set frm = Forms(i)
        If frm.Name <> "frmLogin" Then
            DoCmd.Close acForm, frm.Name
        End If
    Next i
End Sub

' وظيفة التحقق من صحة البيانات
Public Function ValidateRequired(ControlValue As Variant, FieldName As String) As Boolean
    If IsNull(ControlValue) Or Len(Trim(ControlValue & "")) = 0 Then
        MsgBox "يجب إدخال " & FieldName, vbExclamation, "بيانات مطلوبة"
        ValidateRequired = False
    Else
        ValidateRequired = True
    End If
End Function

' وظيفة التحقق من صحة البريد الإلكتروني
Public Function ValidateEmail(Email As String) As Boolean
    Dim EmailPattern As String
    EmailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    
    If Len(Email) = 0 Then
        ValidateEmail = True ' البريد الإلكتروني اختياري
    Else
        ValidateEmail = (Email Like "*@*.*")
        If Not ValidateEmail Then
            MsgBox "البريد الإلكتروني غير صحيح", vbExclamation, "خطأ في البيانات"
        End If
    End If
End Function

' وظيفة التحقق من صحة رقم الهاتف
Public Function ValidatePhone(Phone As String) As Boolean
    If Len(Phone) = 0 Then
        ValidatePhone = True ' رقم الهاتف اختياري
    Else
        If Len(Phone) < 7 Or Len(Phone) > 15 Then
            MsgBox "رقم الهاتف يجب أن يكون بين 7 و 15 رقم", vbExclamation, "خطأ في البيانات"
            ValidatePhone = False
        Else
            ValidatePhone = True
        End If
    End If
End Function

' وظيفة تنسيق العملة
Public Function FormatCurrency(Amount As Currency) As String
    FormatCurrency = Format(Amount, "#,##0.00") & " ريال"
End Function

' وظيفة تنسيق التاريخ
Public Function FormatArabicDate(DateValue As Date) As String
    FormatArabicDate = Format(DateValue, "dd/mm/yyyy")
End Function

' وظيفة الحصول على الرقم التسلسلي التالي
Public Function GetNextSequence(TableName As String, FieldName As String, Prefix As String) As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    Dim NextNumber As Long
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    SQL = "SELECT MAX(Val(Mid(" & FieldName & ", " & (Len(Prefix) + 1) & "))) AS MaxNum " & _
          "FROM " & TableName & " WHERE " & FieldName & " LIKE '" & Prefix & "*'"
    
    Set rs = db.OpenRecordset(SQL)
    
    If IsNull(rs!MaxNum) Then
        NextNumber = 1
    Else
        NextNumber = rs!MaxNum + 1
    End If
    
    GetNextSequence = Prefix & Format(NextNumber, "0000")
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    GetNextSequence = Prefix & "0001"
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة النسخ الاحتياطي
Public Sub BackupDatabase()
    Dim SourcePath As String
    Dim BackupPath As String
    Dim BackupFileName As String
    
    On Error GoTo ErrorHandler
    
    SourcePath = CurrentDb.Name
    BackupFileName = "Backup_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".accdb"
    BackupPath = Left(SourcePath, InStrRev(SourcePath, "\")) & "Backups\" & BackupFileName
    
    ' إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    If Dir(Left(SourcePath, InStrRev(SourcePath, "\")) & "Backups\", vbDirectory) = "" Then
        MkDir Left(SourcePath, InStrRev(SourcePath, "\")) & "Backups\"
    End If
    
    ' نسخ قاعدة البيانات
    FileCopy SourcePath, BackupPath
    
    ' تسجيل النشاط
    LogActivity "نسخ احتياطي", "System", 0, "تم إنشاء نسخة احتياطية: " & BackupFileName
    
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح" & vbCrLf & BackupPath, vbInformation, "نسخ احتياطي"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النسخة الاحتياطية: " & Err.Description, vbCritical, "خطأ"
End Sub

' وظيفة الحصول على إعداد النظام
Public Function GetSystemSetting(SettingName As String) As String
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    SQL = "SELECT SettingValue FROM SystemSettings WHERE SettingName = '" & SettingName & "'"
    
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        GetSystemSetting = rs!SettingValue & ""
    Else
        GetSystemSetting = ""
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    GetSystemSetting = ""
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة تحديث إعداد النظام
Public Sub SetSystemSetting(SettingName As String, SettingValue As String)
    Dim db As DAO.Database
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    ' التحقق من وجود الإعداد
    If DCount("*", "SystemSettings", "SettingName = '" & SettingName & "'") > 0 Then
        ' تحديث الإعداد الموجود
        SQL = "UPDATE SystemSettings SET SettingValue = '" & SettingValue & "', " & _
              "ModifiedBy = " & CurrentUserID & ", ModifiedDate = Now() " & _
              "WHERE SettingName = '" & SettingName & "'"
    Else
        ' إضافة إعداد جديد
        SQL = "INSERT INTO SystemSettings (SettingName, SettingValue, ModifiedBy) " & _
              "VALUES ('" & SettingName & "', '" & SettingValue & "', " & CurrentUserID & ")"
    End If
    
    db.Execute SQL
    
    Set db = Nothing
    Exit Sub
    
ErrorHandler:
    Set db = Nothing
End Sub

' وظيفة تشفير كلمة المرور (بسيط)
Public Function EncryptPassword(Password As String) As String
    Dim i As Integer
    Dim EncryptedPassword As String
    
    For i = 1 To Len(Password)
        EncryptedPassword = EncryptedPassword & Chr(Asc(Mid(Password, i, 1)) + 3)
    Next i
    
    EncryptPassword = EncryptedPassword
End Function

' وظيفة فك تشفير كلمة المرور
Public Function DecryptPassword(EncryptedPassword As String) As String
    Dim i As Integer
    Dim DecryptedPassword As String
    
    For i = 1 To Len(EncryptedPassword)
        DecryptedPassword = DecryptedPassword & Chr(Asc(Mid(EncryptedPassword, i, 1)) - 3)
    Next i
    
    DecryptPassword = DecryptedPassword
End Function
