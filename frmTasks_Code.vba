' كود نموذج إدارة المهام اليومية
' Daily Tasks Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    Me.Caption = "إدارة المهام اليومية"
    Me.RightToLeft = True
    SetFormPermissions
    RefreshData
    UpdateStatistics
End Sub

Private Sub SetFormPermissions()
    Me.cmdAddTask.Enabled = CheckPermission("المهام", "CanAdd")
    Me.cmdEditTask.Enabled = CheckPermission("المهام", "CanEdit")
    Me.cmdDeleteTask.Enabled = CheckPermission("المهام", "CanDelete")
    Me.cmdPrint.Enabled = CheckPermission("المهام", "CanPrint")
End Sub

Private Sub RefreshData()
    ' تحديث قائمة المهام
    Me.lstTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u1.FullName AS AssignedTo, " & _
                            "u2.FullName AS AssignedBy, t.TaskPriority, t.TaskStatus, t.DueDate " & _
                            "FROM ((Tasks t " & _
                            "INNER JOIN Users u1 ON t.AssignedTo = u1.UserID) " & _
                            "INNER JOIN Users u2 ON t.AssignedBy = u2.UserID) " & _
                            "ORDER BY t.DueDate DESC"
    Me.lstTasks.Requery
    
    ' تحديث قائمة مهامي
    Me.lstMyTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u.FullName AS AssignedBy, " & _
                              "t.TaskPriority, t.TaskStatus, t.DueDate " & _
                              "FROM (Tasks t INNER JOIN Users u ON t.AssignedBy = u.UserID) " & _
                              "WHERE t.AssignedTo = " & CurrentUserID & " " & _
                              "ORDER BY t.DueDate DESC"
    Me.lstMyTasks.Requery
End Sub

Private Sub UpdateStatistics()
    ' إحصائيات المهام العامة
    Me.lblTotalTasks.Caption = DCount("*", "Tasks")
    Me.lblNewTasks.Caption = DCount("*", "Tasks", "TaskStatus = 'جديد'")
    Me.lblInProgressTasks.Caption = DCount("*", "Tasks", "TaskStatus = 'قيد التنفيذ'")
    Me.lblCompletedTasks.Caption = DCount("*", "Tasks", "TaskStatus = 'مكتمل'")
    
    ' إحصائيات مهامي
    Me.lblMyTotalTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID)
    Me.lblMyPendingTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID & " AND TaskStatus IN ('جديد', 'قيد التنفيذ')")
    Me.lblMyCompletedTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID & " AND TaskStatus = 'مكتمل'")
    Me.lblMyOverdueTasks.Caption = DCount("*", "Tasks", "AssignedTo = " & CurrentUserID & " AND TaskStatus IN ('جديد', 'قيد التنفيذ') AND DueDate < Date()")
End Sub

Private Sub cmdAddTask_Click()
    If Not CheckPermission("المهام", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة مهمة جديدة", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmTaskDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEditTask_Click()
    If IsNull(Me.lstTasks) Then
        MsgBox "يرجى اختيار مهمة للتعديل", vbExclamation
        Exit Sub
    End If
    DoCmd.OpenForm "frmTaskDetails", acNormal, , "TaskID = " & Me.lstTasks, acFormEdit
End Sub

Private Sub cmdDeleteTask_Click()
    If IsNull(Me.lstTasks) Then
        MsgBox "يرجى اختيار مهمة للحذف", vbExclamation
        Exit Sub
    End If
    
    If MsgBox("هل أنت متأكد من حذف هذه المهمة؟", vbYesNo + vbCritical) = vbYes Then
        CurrentDb.Execute "DELETE FROM Tasks WHERE TaskID = " & Me.lstTasks
        LogActivity "حذف", "Tasks", Me.lstTasks, "حذف مهمة"
        RefreshData
        UpdateStatistics
        MsgBox "تم حذف المهمة بنجاح", vbInformation
    End If
End Sub

Private Sub cmdCompleteMyTask_Click()
    If IsNull(Me.lstMyTasks) Then
        MsgBox "يرجى اختيار مهمة لإكمالها", vbExclamation
        Exit Sub
    End If
    
    ' التحقق من أن المهمة غير مكتملة
    Dim TaskStatus As String
    TaskStatus = DLookup("TaskStatus", "Tasks", "TaskID = " & Me.lstMyTasks)
    
    If TaskStatus = "مكتمل" Then
        MsgBox "هذه المهمة مكتملة بالفعل", vbInformation
        Exit Sub
    End If
    
    If MsgBox("هل تريد تمييز هذه المهمة كمكتملة؟", vbYesNo + vbQuestion) = vbYes Then
        CurrentDb.Execute "UPDATE Tasks SET TaskStatus = 'مكتمل', CompletionDate = Now() WHERE TaskID = " & Me.lstMyTasks
        LogActivity "إكمال", "Tasks", Me.lstMyTasks, "إكمال مهمة"
        RefreshData
        UpdateStatistics
        MsgBox "تم تمييز المهمة كمكتملة", vbInformation
    End If
End Sub

Private Sub cmdStartMyTask_Click()
    If IsNull(Me.lstMyTasks) Then
        MsgBox "يرجى اختيار مهمة لبدء العمل عليها", vbExclamation
        Exit Sub
    End If
    
    ' التحقق من حالة المهمة
    Dim TaskStatus As String
    TaskStatus = DLookup("TaskStatus", "Tasks", "TaskID = " & Me.lstMyTasks)
    
    If TaskStatus <> "جديد" Then
        MsgBox "يمكن بدء المهام الجديدة فقط", vbInformation
        Exit Sub
    End If
    
    CurrentDb.Execute "UPDATE Tasks SET TaskStatus = 'قيد التنفيذ', StartDate = Now() WHERE TaskID = " & Me.lstMyTasks
    LogActivity "بدء", "Tasks", Me.lstMyTasks, "بدء العمل على مهمة"
    RefreshData
    UpdateStatistics
    MsgBox "تم بدء العمل على المهمة", vbInformation
End Sub

Private Sub cmdFilterByStatus_Click()
    Dim StatusFilter As String
    StatusFilter = InputBox("أدخل حالة المهمة للتصفية (جديد، قيد التنفيذ، مكتمل، ملغي):", "تصفية المهام")
    
    If Len(Trim(StatusFilter)) > 0 Then
        Me.lstTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u1.FullName AS AssignedTo, " & _
                                "u2.FullName AS AssignedBy, t.TaskPriority, t.TaskStatus, t.DueDate " & _
                                "FROM ((Tasks t " & _
                                "INNER JOIN Users u1 ON t.AssignedTo = u1.UserID) " & _
                                "INNER JOIN Users u2 ON t.AssignedBy = u2.UserID) " & _
                                "WHERE t.TaskStatus = '" & StatusFilter & "' " & _
                                "ORDER BY t.DueDate DESC"
        Me.lstTasks.Requery
        
        If Me.lstTasks.ListCount = 0 Then
            MsgBox "لم يتم العثور على مهام بهذه الحالة", vbInformation
            RefreshData
        End If
    End If
End Sub

Private Sub cmdFilterByPriority_Click()
    Dim PriorityFilter As String
    PriorityFilter = InputBox("أدخل أولوية المهمة للتصفية (منخفض، متوسط، عالي، عاجل):", "تصفية المهام")
    
    If Len(Trim(PriorityFilter)) > 0 Then
        Me.lstTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u1.FullName AS AssignedTo, " & _
                                "u2.FullName AS AssignedBy, t.TaskPriority, t.TaskStatus, t.DueDate " & _
                                "FROM ((Tasks t " & _
                                "INNER JOIN Users u1 ON t.AssignedTo = u1.UserID) " & _
                                "INNER JOIN Users u2 ON t.AssignedBy = u2.UserID) " & _
                                "WHERE t.TaskPriority = '" & PriorityFilter & "' " & _
                                "ORDER BY t.DueDate DESC"
        Me.lstTasks.Requery
        
        If Me.lstTasks.ListCount = 0 Then
            MsgBox "لم يتم العثور على مهام بهذه الأولوية", vbInformation
            RefreshData
        End If
    End If
End Sub

Private Sub cmdShowOverdue_Click()
    ' عرض المهام المتأخرة
    Me.lstTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u1.FullName AS AssignedTo, " & _
                            "u2.FullName AS AssignedBy, t.TaskPriority, t.TaskStatus, t.DueDate " & _
                            "FROM ((Tasks t " & _
                            "INNER JOIN Users u1 ON t.AssignedTo = u1.UserID) " & _
                            "INNER JOIN Users u2 ON t.AssignedBy = u2.UserID) " & _
                            "WHERE t.TaskStatus IN ('جديد', 'قيد التنفيذ') AND t.DueDate < Date() " & _
                            "ORDER BY t.DueDate"
    Me.lstTasks.Requery
    
    If Me.lstTasks.ListCount = 0 Then
        MsgBox "لا توجد مهام متأخرة", vbInformation
    Else
        MsgBox "تم عرض " & Me.lstTasks.ListCount & " مهمة متأخرة", vbInformation
    End If
End Sub

Private Sub cmdShowToday_Click()
    ' عرض مهام اليوم
    Me.lstTasks.RowSource = "SELECT t.TaskID, t.TaskTitle, u1.FullName AS AssignedTo, " & _
                            "u2.FullName AS AssignedBy, t.TaskPriority, t.TaskStatus, t.DueDate " & _
                            "FROM ((Tasks t " & _
                            "INNER JOIN Users u1 ON t.AssignedTo = u1.UserID) " & _
                            "INNER JOIN Users u2 ON t.AssignedBy = u2.UserID) " & _
                            "WHERE t.DueDate = Date() " & _
                            "ORDER BY t.TaskPriority DESC"
    Me.lstTasks.Requery
    
    If Me.lstTasks.ListCount = 0 Then
        MsgBox "لا توجد مهام مستحقة اليوم", vbInformation
    Else
        MsgBox "تم عرض " & Me.lstTasks.ListCount & " مهمة مستحقة اليوم", vbInformation
    End If
End Sub

Private Sub cmdRefresh_Click()
    RefreshData
    UpdateStatistics
    MsgBox "تم تحديث البيانات", vbInformation
End Sub

Private Sub lstTasks_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstTasks) Then
        cmdEditTask_Click
    End If
End Sub

Private Sub lstMyTasks_DblClick(Cancel As Integer)
    If Not IsNull(Me.lstMyTasks) Then
        DoCmd.OpenForm "frmTaskDetails", acNormal, , "TaskID = " & Me.lstMyTasks, acFormReadOnly
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Tasks", 0, "إغلاق نموذج إدارة المهام"
End Sub
