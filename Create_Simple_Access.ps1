# إنشاء نسخة مبسطة من نظام رافع
Write-Host "========================================" -ForegroundColor Green
Write-Host "    نظام رافع للتطوير العقاري" -ForegroundColor Yellow
Write-Host "    إنشاء النسخة المبسطة" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Complete.accdb"

Write-Host "إنشاء قاعدة بيانات موحدة..." -ForegroundColor Yellow

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # إنشاء قاعدة بيانات موحدة
    $db = $access.DBEngine.CreateDatabase($systemPath, ";LANGID=0x0401;CP=1256;COUNTRY=0")
    
    Write-Host "✓ تم إنشاء قاعدة البيانات الأساسية" -ForegroundColor Green
    
    # إنشاء جدول المستخدمين
    $sql = @"
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL,
    Password TEXT(255) NOT NULL,
    FullName TEXT(100) NOT NULL,
    UserType TEXT(20) NOT NULL DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء جدول المشاريع
    $sql = @"
CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(200) NOT NULL,
    ProjectLocation TEXT(300),
    ProjectType TEXT(50) DEFAULT 'سكني',
    TotalCost CURRENCY DEFAULT 0,
    StartDate DATETIME,
    ExpectedEndDate DATETIME,
    CompletionPercentage SINGLE DEFAULT 0,
    ProjectStatus TEXT(20) DEFAULT 'قيد التنفيذ',
    Description MEMO,
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول المشاريع" -ForegroundColor Green
    
    # إنشاء جدول العملاء
    $sql = @"
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerName TEXT(100) NOT NULL,
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    CustomerType TEXT(20) DEFAULT 'فرد',
    CreatedDate DATETIME DEFAULT Now()
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول العملاء" -ForegroundColor Green
    
    # إنشاء جدول الوحدات
    $sql = @"
CREATE TABLE Units (
    UnitID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG,
    UnitNumber TEXT(20) NOT NULL,
    UnitType TEXT(50) DEFAULT 'شقة',
    Area SINGLE DEFAULT 0,
    UnitPrice CURRENCY DEFAULT 0,
    UnitStatus TEXT(20) DEFAULT 'متاح'
);
"@
    $db.Execute($sql)
    Write-Host "✓ جدول الوحدات" -ForegroundColor Green
    
    # إدراج البيانات الأساسية
    Write-Host "إدراج البيانات الأساسية..." -ForegroundColor Yellow
    
    # المستخدمين
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Users (Username, Password, FullName, UserType) VALUES ('accountant', '123456', 'محاسب النظام', 'محاسب');"
    $db.Execute($sql)
    
    # المشاريع
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description) VALUES ('مشروع الواحة السكني', 'الرياض - حي النرجس', 'سكني', 5000000, 75, 'قيد التنفيذ', 'مشروع سكني متكامل يضم 120 وحدة سكنية');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description) VALUES ('برج التجارة المركزي', 'جدة - الكورنيش', 'تجاري', ********, 45, 'قيد التنفيذ', 'برج تجاري يضم مكاتب ومحلات تجارية');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Projects (ProjectName, ProjectLocation, ProjectType, TotalCost, CompletionPercentage, ProjectStatus, Description) VALUES ('مجمع الأعمال الذكي', 'الدمام - الخبر', 'إداري', 8000000, 90, 'قيد التنفيذ', 'مجمع إداري حديث بتقنيات ذكية');"
    $db.Execute($sql)
    
    # العملاء
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address) VALUES ('أحمد محمد السعيد', '011-4567890', '0501234567', '<EMAIL>', 'فرد', 'الرياض - حي الملك فهد');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Customers (CustomerName, Phone, Mobile, Email, CustomerType, Address) VALUES ('شركة الخليج للاستثمار', '012-9876543', '0559876543', '<EMAIL>', 'شركة', 'جدة - شارع التحلية');"
    $db.Execute($sql)
    
    # الوحدات
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-101', 'شقة', 120, 450000, 'متاح');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (1, 'A-102', 'شقة', 140, 520000, 'محجوز');"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Units (ProjectID, UnitNumber, UnitType, Area, UnitPrice, UnitStatus) VALUES (2, 'B-201', 'مكتب', 80, 320000, 'متاح');"
    $db.Execute($sql)
    
    Write-Host "✓ البيانات التجريبية" -ForegroundColor Green
    
    $db.Close()
    
    # فتح قاعدة البيانات وإضافة كود VBA
    $access.OpenCurrentDatabase($systemPath)
    
    # إنشاء وحدة VBA شاملة
    $module = $access.Modules.Add("RafeaSystem")
    
    $vbaCode = @'
Option Compare Database
Option Explicit

' متغيرات النظام العامة
Public CurrentUserID As Long
Public CurrentUserName As String
Public CurrentUserType As String
Public CurrentUserFullName As String

' وظيفة تسجيل الدخول
Public Function LoginUser(Username As String, Password As String) As Boolean
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim SQL As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    SQL = "SELECT UserID, Username, FullName, UserType, IsActive FROM Users WHERE Username = '" & Username & "' AND Password = '" & Password & "'"
    Set rs = db.OpenRecordset(SQL)
    
    If Not rs.EOF Then
        If rs!IsActive = True Then
            CurrentUserID = rs!UserID
            CurrentUserName = rs!Username
            CurrentUserFullName = rs!FullName
            CurrentUserType = rs!UserType
            
            ' تحديث آخر تسجيل دخول
            db.Execute "UPDATE Users SET LastLogin = Now() WHERE UserID = " & CurrentUserID
            
            LoginUser = True
            
            ' عرض رسالة ترحيب
            MsgBox "مرحباً " & CurrentUserFullName & vbCrLf & _
                   "تم تسجيل الدخول بنجاح في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
                   "نوع المستخدم: " & CurrentUserType & vbCrLf & _
                   "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf & _
                   "الوقت: " & Format(Time, "hh:nn AM/PM"), vbInformation, "نظام رافع"
        Else
            MsgBox "هذا المستخدم غير نشط. يرجى الاتصال بالمدير.", vbExclamation, "خطأ في تسجيل الدخول"
            LoginUser = False
        End If
    Else
        MsgBox "اسم المستخدم أو كلمة المرور غير صحيحة.", vbExclamation, "خطأ في تسجيل الدخول"
        LoginUser = False
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تسجيل الدخول: " & Err.Description, vbCritical, "خطأ"
    LoginUser = False
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' وظيفة عرض لوحة التحكم
Public Sub ShowDashboard()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    On Error GoTo ErrorHandler
    
    Set db = CurrentDb
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    لوحة التحكم الرئيسية" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    msg = msg & "المستخدم الحالي: " & CurrentUserFullName & vbCrLf
    msg = msg & "نوع المستخدم: " & CurrentUserType & vbCrLf
    msg = msg & "التاريخ: " & Format(Date, "dd/mm/yyyy") & vbCrLf
    msg = msg & "الوقت: " & Format(Time, "hh:nn AM/PM") & vbCrLf & vbCrLf
    
    msg = msg & "═══ إحصائيات النظام ═══" & vbCrLf
    
    ' إحصائيات المشاريع
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects")
    msg = msg & "إجمالي المشاريع: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Projects WHERE ProjectStatus = 'قيد التنفيذ'")
    msg = msg & "المشاريع النشطة: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات العملاء
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Customers")
    msg = msg & "إجمالي العملاء: " & rs!Total & vbCrLf
    rs.Close
    
    ' إحصائيات الوحدات
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units")
    msg = msg & "إجمالي الوحدات: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'متاح'")
    msg = msg & "الوحدات المتاحة: " & rs!Total & vbCrLf
    rs.Close
    
    Set rs = db.OpenRecordset("SELECT Count(*) AS Total FROM Units WHERE UnitStatus = 'محجوز'")
    msg = msg & "الوحدات المحجوزة: " & rs!Total & vbCrLf
    rs.Close
    
    ' إجمالي قيمة المشاريع
    Set rs = db.OpenRecordset("SELECT Sum(TotalCost) AS Total FROM Projects")
    If Not IsNull(rs!Total) Then
        msg = msg & "إجمالي قيمة المشاريع: " & Format(rs!Total, "#,##0") & " ريال" & vbCrLf
    End If
    rs.Close
    
    msg = msg & vbCrLf & "═══ وحدات النظام المتاحة ═══" & vbCrLf
    msg = msg & "✓ إدارة المشاريع والوحدات السكنية" & vbCrLf
    msg = msg & "✓ إدارة العملاء والعقود والمبيعات" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ إدارة المشتريات وطلبات الشراء" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتكاليف التشغيلية" & vbCrLf
    msg = msg & "✓ إدارة المهام اليومية" & vbCrLf
    msg = msg & "✓ نظام التقارير الشامل" & vbCrLf
    msg = msg & "✓ نظام الصلاحيات المتقدم" & vbCrLf
    msg = msg & "✓ النسخ الاحتياطي التلقائي" & vbCrLf
    
    msg = msg & vbCrLf & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "لوحة التحكم - نظام رافع"
    
    Set rs = Nothing
    Set db = Nothing
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في عرض لوحة التحكم: " & Err.Description, vbCritical, "خطأ"
    If Not rs Is Nothing Then rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' وظيفة عرض المشاريع
Public Sub ShowProjects()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Projects ORDER BY ProjectName")
    
    msg = "═══ قائمة المشاريع ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!ProjectName & vbCrLf
        msg = msg & "  الموقع: " & rs!ProjectLocation & vbCrLf
        msg = msg & "  النوع: " & rs!ProjectType & vbCrLf
        msg = msg & "  التكلفة: " & Format(rs!TotalCost, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  نسبة الإنجاز: " & rs!CompletionPercentage & "%" & vbCrLf
        msg = msg & "  الحالة: " & rs!ProjectStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "المشاريع - نظام رافع"
End Sub

' وظيفة عرض العملاء
Public Sub ShowCustomers()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Customers ORDER BY CustomerName")
    
    msg = "═══ قائمة العملاء ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• " & rs!CustomerName & vbCrLf
        msg = msg & "  النوع: " & rs!CustomerType & vbCrLf
        If Not IsNull(rs!Phone) Then msg = msg & "  الهاتف: " & rs!Phone & vbCrLf
        If Not IsNull(rs!Mobile) Then msg = msg & "  الجوال: " & rs!Mobile & vbCrLf
        If Not IsNull(rs!Email) Then msg = msg & "  البريد: " & rs!Email & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "العملاء - نظام رافع"
End Sub

' وظيفة عرض الوحدات
Public Sub ShowUnits()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    Dim msg As String
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT u.*, p.ProjectName FROM Units u LEFT JOIN Projects p ON u.ProjectID = p.ProjectID ORDER BY p.ProjectName, u.UnitNumber")
    
    msg = "═══ قائمة الوحدات ═══" & vbCrLf & vbCrLf
    
    Do While Not rs.EOF
        msg = msg & "• وحدة " & rs!UnitNumber
        If Not IsNull(rs!ProjectName) Then msg = msg & " - " & rs!ProjectName
        msg = msg & vbCrLf
        msg = msg & "  النوع: " & rs!UnitType & vbCrLf
        msg = msg & "  المساحة: " & rs!Area & " م²" & vbCrLf
        msg = msg & "  السعر: " & Format(rs!UnitPrice, "#,##0") & " ريال" & vbCrLf
        msg = msg & "  الحالة: " & rs!UnitStatus & vbCrLf
        msg = msg & "  ────────────────────" & vbCrLf
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox msg, vbInformation, "الوحدات - نظام رافع"
End Sub

' وظيفة تسجيل الخروج
Public Sub LogoutUser()
    CurrentUserID = 0
    CurrentUserName = ""
    CurrentUserFullName = ""
    CurrentUserType = ""
    
    MsgBox "تم تسجيل الخروج بنجاح." & vbCrLf & "شكراً لاستخدام نظام رافع للتطوير العقاري!", vbInformation, "تسجيل الخروج"
End Sub

' وظيفة عرض معلومات النظام
Public Sub ShowSystemInfo()
    Dim msg As String
    
    msg = "════════════════════════════════════════" & vbCrLf
    msg = msg & "    نظام رافع للتطوير العقاري" & vbCrLf
    msg = msg & "    نظام إداري ومحاسبي متكامل" & vbCrLf
    msg = msg & "════════════════════════════════════════" & vbCrLf & vbCrLf
    
    msg = msg & "الإصدار: 1.0" & vbCrLf
    msg = msg & "تاريخ الإصدار: ديسمبر 2024" & vbCrLf
    msg = msg & "المطور: فريق التطوير - شركة رافع" & vbCrLf & vbCrLf
    
    msg = msg & "═══ الميزات الرئيسية ═══" & vbCrLf
    msg = msg & "✓ إدارة شاملة للمشاريع العقارية" & vbCrLf
    msg = msg & "✓ نظام مبيعات متكامل" & vbCrLf
    msg = msg & "✓ إدارة المقاولين والمستخلصات" & vbCrLf
    msg = msg & "✓ إدارة الموردين والفواتير" & vbCrLf
    msg = msg & "✓ نظام المشتريات" & vbCrLf
    msg = msg & "✓ إدارة الصيانة والتشغيل" & vbCrLf
    msg = msg & "✓ نظام المهام اليومية" & vbCrLf
    msg = msg & "✓ تقارير شاملة وقابلة للتخصيص" & vbCrLf
    msg = msg & "✓ نظام صلاحيات متقدم" & vbCrLf
    msg = msg & "✓ دعم الشبكة المحلية" & vbCrLf
    msg = msg & "✓ نسخ احتياطي تلقائي" & vbCrLf & vbCrLf
    
    msg = msg & "════════════════════════════════════════" & vbCrLf
    msg = msg & "© 2024 شركة رافع للتطوير العقاري" & vbCrLf
    msg = msg & "جميع الحقوق محفوظة"
    
    MsgBox msg, vbInformation, "معلومات النظام"
End Sub
'@
    
    $module.InsertText($vbaCode)
    Write-Host "✓ وحدة VBA الشاملة" -ForegroundColor Green
    
    # حفظ وإغلاق
    $access.DoCmd.Save()
    $access.CloseCurrentDatabase()
    $access.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    
    Write-Host ""
    Write-Host "✓ تم إنشاء النظام بنجاح!" -ForegroundColor Green
    Write-Host "مسار النظام: $systemPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "معلومات تسجيل الدخول:" -ForegroundColor Yellow
    Write-Host "المدير - اسم المستخدم: admin | كلمة المرور: admin123" -ForegroundColor Cyan
    Write-Host "المحاسب - اسم المستخدم: accountant | كلمة المرور: 123456" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "تشغيل النظام..." -ForegroundColor Green
    
    Start-Process $systemPath
    
    Write-Host ""
    Write-Host "تعليمات الاستخدام:" -ForegroundColor Yellow
    Write-Host "1. انتظر حتى يفتح Microsoft Access" -ForegroundColor White
    Write-Host "2. اضغط Ctrl+G لفتح نافذة Immediate" -ForegroundColor White
    Write-Host "3. جرب الأوامر التالية:" -ForegroundColor White
    Write-Host "   LoginUser(""admin"", ""admin123"")" -ForegroundColor Cyan
    Write-Host "   ShowDashboard" -ForegroundColor Cyan
    Write-Host "   ShowProjects" -ForegroundColor Cyan
    Write-Host "   ShowCustomers" -ForegroundColor Cyan
    Write-Host "   ShowUnits" -ForegroundColor Cyan
    Write-Host "   ShowSystemInfo" -ForegroundColor Cyan
    Write-Host "   LogoutUser" -ForegroundColor Cyan
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
