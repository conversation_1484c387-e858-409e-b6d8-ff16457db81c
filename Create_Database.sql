-- إنشاء قاعدة البيانات الأساسية لنظام رافع للتطوير العقاري
-- Create Database Script for Rafea Real Estate Development System

-- جدول المستخدمين
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL UNIQUE,
    Password TEXT(255) NOT NULL,
    FullName TEXT(100) NOT NULL,
    UserType TEXT(20) NOT NULL DEFAULT 'مستخدم',
    IsActive YESNO DEFAULT Yes,
    CreatedDate DATETIME DEFAULT Now(),
    LastLogin DATETIME,
    CONSTRAINT chk_UserType CHECK (UserType IN ('مدير', 'محاسب', 'مهندس', 'مبيعات', 'مستخدم'))
);

-- جدول الصلاحيات
CREATE TABLE Permissions (
    PermissionID AUTOINCREMENT PRIMARY KEY,
    UserID LONG NOT NULL,
    ModuleName TEXT(50) NOT NULL,
    CanView YESNO DEFAULT Yes,
    CanAdd YESNO DEFAULT No,
    CanEdit YESNO DEFAULT No,
    CanDelete YESNO DEFAULT No,
    CanPrint YESNO DEFAULT Yes,
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- جدول المشاريع
CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(200) NOT NULL,
    ProjectLocation TEXT(300),
    ProjectType TEXT(50) DEFAULT 'سكني',
    TotalCost CURRENCY DEFAULT 0,
    StartDate DATETIME,
    ExpectedEndDate DATETIME,
    ActualEndDate DATETIME,
    CompletionPercentage SINGLE DEFAULT 0,
    ProjectStatus TEXT(20) DEFAULT 'قيد التنفيذ',
    Description MEMO,
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT Now(),
    CONSTRAINT chk_ProjectType CHECK (ProjectType IN ('سكني', 'تجاري', 'إداري', 'مختلط')),
    CONSTRAINT chk_ProjectStatus CHECK (ProjectStatus IN ('قيد التنفيذ', 'مكتمل', 'متوقف', 'ملغي')),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول الوحدات السكنية
CREATE TABLE Units (
    UnitID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    UnitNumber TEXT(20) NOT NULL,
    UnitType TEXT(50) DEFAULT 'شقة',
    Area SINGLE DEFAULT 0,
    Floor INTEGER DEFAULT 0,
    Rooms INTEGER DEFAULT 0,
    Bathrooms INTEGER DEFAULT 0,
    UnitPrice CURRENCY DEFAULT 0,
    UnitStatus TEXT(20) DEFAULT 'متاح',
    CONSTRAINT chk_UnitType CHECK (UnitType IN ('شقة', 'فيلا', 'محل', 'مكتب', 'مستودع')),
    CONSTRAINT chk_UnitStatus CHECK (UnitStatus IN ('متاح', 'محجوز', 'مباع', 'تحت الصيانة')),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID)
);

-- جدول العملاء
CREATE TABLE Customers (
    CustomerID AUTOINCREMENT PRIMARY KEY,
    CustomerName TEXT(100) NOT NULL,
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    NationalID TEXT(20),
    CustomerType TEXT(20) DEFAULT 'فرد',
    Notes MEMO,
    CreatedDate DATETIME DEFAULT Now(),
    CONSTRAINT chk_CustomerType CHECK (CustomerType IN ('فرد', 'شركة'))
);

-- جدول العقود
CREATE TABLE Contracts (
    ContractID AUTOINCREMENT PRIMARY KEY,
    CustomerID LONG NOT NULL,
    UnitID LONG NOT NULL,
    ContractDate DATETIME DEFAULT Now(),
    TotalAmount CURRENCY NOT NULL,
    DownPayment CURRENCY DEFAULT 0,
    InstallmentAmount CURRENCY DEFAULT 0,
    NumberOfInstallments INTEGER DEFAULT 0,
    ContractStatus TEXT(20) DEFAULT 'نشط',
    Notes MEMO,
    CreatedBy LONG,
    CONSTRAINT chk_ContractStatus CHECK (ContractStatus IN ('نشط', 'مكتمل', 'ملغي')),
    FOREIGN KEY (CustomerID) REFERENCES Customers(CustomerID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول المدفوعات
CREATE TABLE Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    ContractID LONG NOT NULL,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentAmount CURRENCY NOT NULL,
    PaymentMethod TEXT(20) DEFAULT 'نقدي',
    PaymentStatus TEXT(20) DEFAULT 'مدفوع',
    CheckNumber TEXT(50),
    BankName TEXT(100),
    Notes MEMO,
    ReceivedBy LONG,
    CONSTRAINT chk_PaymentMethod CHECK (PaymentMethod IN ('نقدي', 'شيك', 'تحويل', 'بطاقة')),
    CONSTRAINT chk_PaymentStatus CHECK (PaymentStatus IN ('مدفوع', 'معلق', 'مرتد')),
    FOREIGN KEY (ContractID) REFERENCES Contracts(ContractID),
    FOREIGN KEY (ReceivedBy) REFERENCES Users(UserID)
);

-- جدول المقاولين
CREATE TABLE Contractors (
    ContractorID AUTOINCREMENT PRIMARY KEY,
    ContractorName TEXT(100) NOT NULL,
    CompanyName TEXT(150),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    TaxNumber TEXT(20),
    Specialization TEXT(100),
    IsActive YESNO DEFAULT Yes
);

-- جدول عقود المقاولين
CREATE TABLE ContractorContracts (
    ContractorContractID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    ContractorID LONG NOT NULL,
    ContractDate DATETIME DEFAULT Now(),
    ContractAmount CURRENCY NOT NULL,
    WorkDescription MEMO,
    StartDate DATETIME,
    EndDate DATETIME,
    ContractStatus TEXT(20) DEFAULT 'نشط',
    CONSTRAINT chk_ContractorStatus CHECK (ContractStatus IN ('نشط', 'مكتمل', 'ملغي', 'متوقف')),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (ContractorID) REFERENCES Contractors(ContractorID)
);

-- جدول المستخلصات
CREATE TABLE Extracts (
    ExtractID AUTOINCREMENT PRIMARY KEY,
    ContractorContractID LONG NOT NULL,
    ExtractNumber TEXT(20) NOT NULL,
    ExtractDate DATETIME DEFAULT Now(),
    ExtractAmount CURRENCY NOT NULL,
    WorkPercentage SINGLE DEFAULT 0,
    ExtractStatus TEXT(20) DEFAULT 'قيد المراجعة',
    ApprovedBy LONG,
    ApprovalDate DATETIME,
    Notes MEMO,
    CONSTRAINT chk_ExtractStatus CHECK (ExtractStatus IN ('معتمد', 'قيد المراجعة', 'مرفوض')),
    FOREIGN KEY (ContractorContractID) REFERENCES ContractorContracts(ContractorContractID),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID)
);

-- جدول مدفوعات المقاولين
CREATE TABLE ContractorPayments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    ExtractID LONG NOT NULL,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentAmount CURRENCY NOT NULL,
    PaymentMethod TEXT(20) DEFAULT 'شيك',
    CheckNumber TEXT(50),
    BankName TEXT(100),
    PaidBy LONG,
    Notes MEMO,
    CONSTRAINT chk_ContractorPaymentMethod CHECK (PaymentMethod IN ('نقدي', 'شيك', 'تحويل')),
    FOREIGN KEY (ExtractID) REFERENCES Extracts(ExtractID),
    FOREIGN KEY (PaidBy) REFERENCES Users(UserID)
);

-- جدول الموردين
CREATE TABLE Suppliers (
    SupplierID AUTOINCREMENT PRIMARY KEY,
    SupplierName TEXT(100) NOT NULL,
    CompanyName TEXT(150),
    Phone TEXT(20),
    Mobile TEXT(20),
    Email TEXT(100),
    Address TEXT(300),
    TaxNumber TEXT(20),
    SupplierType TEXT(50),
    IsActive YESNO DEFAULT Yes
);

-- جدول الفواتير
CREATE TABLE Invoices (
    InvoiceID AUTOINCREMENT PRIMARY KEY,
    SupplierID LONG NOT NULL,
    ProjectID LONG,
    InvoiceNumber TEXT(30) NOT NULL,
    InvoiceDate DATETIME DEFAULT Now(),
    TotalAmount CURRENCY NOT NULL,
    TaxAmount CURRENCY DEFAULT 0,
    NetAmount CURRENCY NOT NULL,
    InvoiceStatus TEXT(20) DEFAULT 'قيد المراجعة',
    DueDate DATETIME,
    ReceivedBy LONG,
    Notes MEMO,
    CONSTRAINT chk_InvoiceStatus CHECK (InvoiceStatus IN ('معتمد', 'قيد المراجعة', 'مدفوع', 'مرفوض')),
    FOREIGN KEY (SupplierID) REFERENCES Suppliers(SupplierID),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (ReceivedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل الفواتير
CREATE TABLE InvoiceDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    InvoiceID LONG NOT NULL,
    ItemDescription TEXT(200) NOT NULL,
    Quantity SINGLE DEFAULT 1,
    UnitPrice CURRENCY NOT NULL,
    TotalPrice CURRENCY NOT NULL,
    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID)
);

-- جدول مدفوعات الموردين
CREATE TABLE SupplierPayments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    InvoiceID LONG NOT NULL,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentAmount CURRENCY NOT NULL,
    PaymentMethod TEXT(20) DEFAULT 'شيك',
    CheckNumber TEXT(50),
    BankName TEXT(100),
    PaidBy LONG,
    Notes MEMO,
    CONSTRAINT chk_SupplierPaymentMethod CHECK (PaymentMethod IN ('نقدي', 'شيك', 'تحويل')),
    FOREIGN KEY (InvoiceID) REFERENCES Invoices(InvoiceID),
    FOREIGN KEY (PaidBy) REFERENCES Users(UserID)
);

-- جدول طلبات الشراء
CREATE TABLE PurchaseRequests (
    RequestID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG,
    RequestDate DATETIME DEFAULT Now(),
    RequestedBy LONG NOT NULL,
    RequestStatus TEXT(20) DEFAULT 'قيد المراجعة',
    TotalEstimatedCost CURRENCY DEFAULT 0,
    ApprovedBy LONG,
    ApprovalDate DATETIME,
    Notes MEMO,
    CONSTRAINT chk_RequestStatus CHECK (RequestStatus IN ('قيد المراجعة', 'معتمد', 'مرفوض', 'مكتمل')),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (RequestedBy) REFERENCES Users(UserID),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID)
);

-- جدول تفاصيل طلبات الشراء
CREATE TABLE PurchaseRequestDetails (
    DetailID AUTOINCREMENT PRIMARY KEY,
    RequestID LONG NOT NULL,
    ItemDescription TEXT(200) NOT NULL,
    Quantity SINGLE DEFAULT 1,
    EstimatedUnitPrice CURRENCY DEFAULT 0,
    EstimatedTotalPrice CURRENCY DEFAULT 0,
    Urgency TEXT(20) DEFAULT 'عادي',
    CONSTRAINT chk_Urgency CHECK (Urgency IN ('عادي', 'مستعجل', 'طارئ')),
    FOREIGN KEY (RequestID) REFERENCES PurchaseRequests(RequestID)
);

-- جدول أعمال الصيانة
CREATE TABLE Maintenance (
    MaintenanceID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG,
    UnitID LONG,
    MaintenanceType TEXT(50) DEFAULT 'إصلاحية',
    Description MEMO NOT NULL,
    RequestDate DATETIME DEFAULT Now(),
    ScheduledDate DATETIME,
    CompletionDate DATETIME,
    MaintenanceStatus TEXT(20) DEFAULT 'مطلوب',
    Cost CURRENCY DEFAULT 0,
    AssignedTo TEXT(100),
    RequestedBy LONG,
    Notes MEMO,
    CONSTRAINT chk_MaintenanceType CHECK (MaintenanceType IN ('وقائية', 'إصلاحية', 'طارئة')),
    CONSTRAINT chk_MaintenanceStatus CHECK (MaintenanceStatus IN ('مطلوب', 'قيد التنفيذ', 'مكتمل', 'ملغي')),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (UnitID) REFERENCES Units(UnitID),
    FOREIGN KEY (RequestedBy) REFERENCES Users(UserID)
);

-- جدول التكاليف التشغيلية
CREATE TABLE OperatingCosts (
    CostID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    CostType TEXT(50) NOT NULL,
    CostDate DATETIME DEFAULT Now(),
    Amount CURRENCY NOT NULL,
    Description TEXT(200),
    RecordedBy LONG,
    Notes MEMO,
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (RecordedBy) REFERENCES Users(UserID)
);

-- جدول المهام
CREATE TABLE Tasks (
    TaskID AUTOINCREMENT PRIMARY KEY,
    TaskTitle TEXT(200) NOT NULL,
    TaskDescription MEMO,
    AssignedTo LONG NOT NULL,
    AssignedBy LONG NOT NULL,
    ProjectID LONG,
    TaskPriority TEXT(20) DEFAULT 'متوسط',
    TaskStatus TEXT(20) DEFAULT 'جديد',
    StartDate DATETIME,
    DueDate DATETIME,
    CompletionDate DATETIME,
    CreatedDate DATETIME DEFAULT Now(),
    Notes MEMO,
    CONSTRAINT chk_TaskPriority CHECK (TaskPriority IN ('منخفض', 'متوسط', 'عالي', 'عاجل')),
    CONSTRAINT chk_TaskStatus CHECK (TaskStatus IN ('جديد', 'قيد التنفيذ', 'مكتمل', 'ملغي')),
    FOREIGN KEY (AssignedTo) REFERENCES Users(UserID),
    FOREIGN KEY (AssignedBy) REFERENCES Users(UserID),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID)
);

-- جدول سجل النشاطات
CREATE TABLE ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID LONG NOT NULL,
    ActivityType TEXT(50) NOT NULL,
    TableName TEXT(50),
    RecordID LONG,
    ActivityDescription TEXT(300),
    ActivityDate DATETIME DEFAULT Now(),
    IPAddress TEXT(15),
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- جدول إعدادات النظام
CREATE TABLE SystemSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingName TEXT(100) NOT NULL UNIQUE,
    SettingValue TEXT(500),
    SettingDescription TEXT(300),
    ModifiedBy LONG,
    ModifiedDate DATETIME DEFAULT Now(),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserID)
);

-- إدراج البيانات الأساسية
INSERT INTO Users (Username, Password, FullName, UserType) 
VALUES ('admin', 'admin123', 'مدير النظام', 'مدير');

INSERT INTO SystemSettings (SettingName, SettingValue, SettingDescription) 
VALUES 
('CompanyName', 'شركة رافع للتطوير العقاري', 'اسم الشركة'),
('CompanyAddress', '', 'عنوان الشركة'),
('CompanyPhone', '', 'هاتف الشركة'),
('TaxRate', '15', 'معدل الضريبة المضافة'),
('CurrencySymbol', 'ريال', 'رمز العملة');
