' كود نموذج التقارير
' Reports Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    Me.Caption = "التقارير"
    Me.RightToLeft = True
    SetFormPermissions
    LoadReportCategories
End Sub

Private Sub SetFormPermissions()
    ' التحقق من صلاحيات التقارير
    If Not CheckPermission("التقارير", "CanView") Then
        MsgBox "ليس لديك صلاحية لعرض التقارير", vbExclamation
        DoCmd.Close acForm, Me.Name
        Exit Sub
    End If
End Sub

Private Sub LoadReportCategories()
    ' تحميل فئات التقارير
    Me.lstReportCategories.RowSource = "'تقارير المشاريع';'تقارير المبيعات';'تقارير المقاولين';'تقارير الموردين';'تقارير المشتريات';'تقارير الصيانة';'تقارير المهام';'التقارير المالية';'التقارير الإحصائية'"
    Me.lstReportCategories.Requery
End Sub

Private Sub lstReportCategories_AfterUpdate()
    ' تحديث قائمة التقارير حسب الفئة المختارة
    Select Case Me.lstReportCategories
        Case "تقارير المشاريع"
            LoadProjectReports
        Case "تقارير المبيعات"
            LoadSalesReports
        Case "تقارير المقاولين"
            LoadContractorReports
        Case "تقارير الموردين"
            LoadSupplierReports
        Case "تقارير المشتريات"
            LoadPurchaseReports
        Case "تقارير الصيانة"
            LoadMaintenanceReports
        Case "تقارير المهام"
            LoadTaskReports
        Case "التقارير المالية"
            LoadFinancialReports
        Case "التقارير الإحصائية"
            LoadStatisticalReports
    End Select
End Sub

Private Sub LoadProjectReports()
    Me.lstReports.RowSource = "'تقرير جميع المشاريع';'تقرير المشاريع النشطة';'تقرير المشاريع المكتملة';'تقرير تقدم المشاريع';'تقرير تكاليف المشاريع';'تقرير الوحدات السكنية';'تقرير الوحدات المتاحة';'تقرير الوحدات المباعة'"
    Me.lstReports.Requery
End Sub

Private Sub LoadSalesReports()
    Me.lstReports.RowSource = "'تقرير جميع العملاء';'تقرير العقود النشطة';'تقرير المبيعات الشهرية';'تقرير المدفوعات';'تقرير المتأخرات';'تقرير أداء المبيعات';'تقرير العملاء الجدد';'تقرير الحجوزات'"
    Me.lstReports.Requery
End Sub

Private Sub LoadContractorReports()
    Me.lstReports.RowSource = "'تقرير جميع المقاولين';'تقرير عقود المقاولين';'تقرير المستخلصات';'تقرير المستخلصات المعتمدة';'تقرير مدفوعات المقاولين';'تقرير أداء المقاولين';'تقرير المستخلصات المعلقة'"
    Me.lstReports.Requery
End Sub

Private Sub LoadSupplierReports()
    Me.lstReports.RowSource = "'تقرير جميع الموردين';'تقرير الفواتير';'تقرير الفواتير المعتمدة';'تقرير مدفوعات الموردين';'تقرير المستحقات';'تقرير أداء الموردين';'تقرير الفواتير المعلقة'"
    Me.lstReports.Requery
End Sub

Private Sub LoadPurchaseReports()
    Me.lstReports.RowSource = "'تقرير طلبات الشراء';'تقرير الطلبات المعتمدة';'تقرير الطلبات المعلقة';'تقرير المشتريات الشهرية';'تقرير المشتريات حسب المشروع'"
    Me.lstReports.Requery
End Sub

Private Sub LoadMaintenanceReports()
    Me.lstReports.RowSource = "'تقرير أعمال الصيانة';'تقرير الصيانة المكتملة';'تقرير الصيانة المعلقة';'تقرير التكاليف التشغيلية';'تقرير صيانة المشاريع'"
    Me.lstReports.Requery
End Sub

Private Sub LoadTaskReports()
    Me.lstReports.RowSource = "'تقرير جميع المهام';'تقرير المهام المكتملة';'تقرير المهام المعلقة';'تقرير المهام المتأخرة';'تقرير أداء الموظفين';'تقرير المهام حسب الأولوية'"
    Me.lstReports.Requery
End Sub

Private Sub LoadFinancialReports()
    Me.lstReports.RowSource = "'التقرير المالي الشامل';'تقرير الإيرادات';'تقرير المصروفات';'تقرير الأرباح والخسائر';'تقرير التدفق النقدي';'تقرير الميزانية';'تقرير المقارنات المالية'"
    Me.lstReports.Requery
End Sub

Private Sub LoadStatisticalReports()
    Me.lstReports.RowSource = "'الإحصائيات العامة';'إحصائيات المبيعات';'إحصائيات المشاريع';'إحصائيات الأداء';'إحصائيات شهرية';'إحصائيات سنوية';'مؤشرات الأداء الرئيسية'"
    Me.lstReports.Requery
End Sub

Private Sub cmdPreview_Click()
    If IsNull(Me.lstReports) Then
        MsgBox "يرجى اختيار تقرير للمعاينة", vbExclamation
        Exit Sub
    End If
    
    GenerateReport "Preview"
End Sub

Private Sub cmdPrint_Click()
    If IsNull(Me.lstReports) Then
        MsgBox "يرجى اختيار تقرير للطباعة", vbExclamation
        Exit Sub
    End If
    
    GenerateReport "Print"
End Sub

Private Sub cmdExportPDF_Click()
    If IsNull(Me.lstReports) Then
        MsgBox "يرجى اختيار تقرير للتصدير", vbExclamation
        Exit Sub
    End If
    
    GenerateReport "PDF"
End Sub

Private Sub cmdExportExcel_Click()
    If IsNull(Me.lstReports) Then
        MsgBox "يرجى اختيار تقرير للتصدير", vbExclamation
        Exit Sub
    End If
    
    GenerateReport "Excel"
End Sub

Private Sub GenerateReport(OutputType As String)
    On Error GoTo ErrorHandler
    
    Dim ReportName As String
    Dim FilterCriteria As String
    Dim ExportPath As String
    
    ' تحديد اسم التقرير
    ReportName = GetReportName(Me.lstReports)
    
    If ReportName = "" Then
        MsgBox "التقرير المحدد غير متوفر", vbExclamation
        Exit Sub
    End If
    
    ' بناء معايير التصفية
    FilterCriteria = BuildFilterCriteria()
    
    ' تنفيذ التقرير حسب النوع المطلوب
    Select Case OutputType
        Case "Preview"
            DoCmd.OpenReport ReportName, acViewPreview, , FilterCriteria
            
        Case "Print"
            DoCmd.OpenReport ReportName, acViewNormal, , FilterCriteria
            
        Case "PDF"
            ExportPath = CurrentProject.Path & "\Reports\" & Me.lstReports & "_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".pdf"
            ' إنشاء مجلد التقارير إذا لم يكن موجوداً
            If Dir(CurrentProject.Path & "\Reports\", vbDirectory) = "" Then
                MkDir CurrentProject.Path & "\Reports\"
            End If
            DoCmd.OutputTo acOutputReport, ReportName, acFormatPDF, ExportPath, False, , , acExportQualityPrint
            MsgBox "تم تصدير التقرير إلى:" & vbCrLf & ExportPath, vbInformation
            
        Case "Excel"
            ExportPath = CurrentProject.Path & "\Reports\" & Me.lstReports & "_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".xlsx"
            ' إنشاء مجلد التقارير إذا لم يكن موجوداً
            If Dir(CurrentProject.Path & "\Reports\", vbDirectory) = "" Then
                MkDir CurrentProject.Path & "\Reports\"
            End If
            DoCmd.OutputTo acOutputReport, ReportName, acFormatXLSX, ExportPath, False
            MsgBox "تم تصدير التقرير إلى:" & vbCrLf & ExportPath, vbInformation
    End Select
    
    ' تسجيل النشاط
    LogActivity "تقرير", "Reports", 0, "إنشاء تقرير: " & Me.lstReports & " (" & OutputType & ")"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء التقرير: " & Err.Description, vbCritical
End Sub

Private Function GetReportName(ReportTitle As String) As String
    ' تحويل عنوان التقرير إلى اسم التقرير الفعلي
    Select Case ReportTitle
        Case "تقرير جميع المشاريع"
            GetReportName = "rptAllProjects"
        Case "تقرير المشاريع النشطة"
            GetReportName = "rptActiveProjects"
        Case "تقرير جميع العملاء"
            GetReportName = "rptAllCustomers"
        Case "تقرير العقود النشطة"
            GetReportName = "rptActiveContracts"
        Case "تقرير المبيعات الشهرية"
            GetReportName = "rptMonthlySales"
        Case "تقرير جميع المقاولين"
            GetReportName = "rptAllContractors"
        Case "تقرير المستخلصات"
            GetReportName = "rptExtracts"
        Case "تقرير جميع الموردين"
            GetReportName = "rptAllSuppliers"
        Case "تقرير الفواتير"
            GetReportName = "rptInvoices"
        Case "تقرير طلبات الشراء"
            GetReportName = "rptPurchaseRequests"
        Case "تقرير أعمال الصيانة"
            GetReportName = "rptMaintenance"
        Case "تقرير جميع المهام"
            GetReportName = "rptAllTasks"
        Case "التقرير المالي الشامل"
            GetReportName = "rptFinancialSummary"
        Case "الإحصائيات العامة"
            GetReportName = "rptGeneralStatistics"
        Case Else
            GetReportName = ""
    End Select
End Function

Private Function BuildFilterCriteria() As String
    Dim Criteria As String
    
    ' بناء معايير التصفية حسب التواريخ المحددة
    If Not IsNull(Me.txtFromDate) And Not IsNull(Me.txtToDate) Then
        Criteria = "CreatedDate BETWEEN #" & Me.txtFromDate & "# AND #" & Me.txtToDate & "#"
    ElseIf Not IsNull(Me.txtFromDate) Then
        Criteria = "CreatedDate >= #" & Me.txtFromDate & "#"
    ElseIf Not IsNull(Me.txtToDate) Then
        Criteria = "CreatedDate <= #" & Me.txtToDate & "#"
    End If
    
    ' إضافة معايير أخرى حسب الحاجة
    If Not IsNull(Me.cboProject) Then
        If Len(Criteria) > 0 Then Criteria = Criteria & " AND "
        Criteria = Criteria & "ProjectID = " & Me.cboProject
    End If
    
    If Not IsNull(Me.cboStatus) Then
        If Len(Criteria) > 0 Then Criteria = Criteria & " AND "
        Criteria = Criteria & "Status = '" & Me.cboStatus & "'"
    End If
    
    BuildFilterCriteria = Criteria
End Function

Private Sub cmdClearFilters_Click()
    ' مسح جميع المرشحات
    Me.txtFromDate = Null
    Me.txtToDate = Null
    Me.cboProject = Null
    Me.cboStatus = Null
    MsgBox "تم مسح جميع المرشحات", vbInformation
End Sub

Private Sub cmdSetDateRange_Click()
    ' تعيين نطاق تاريخي سريع
    Dim DateRange As String
    DateRange = InputBox("اختر النطاق الزمني:" & vbCrLf & _
                        "1 - هذا الشهر" & vbCrLf & _
                        "2 - الشهر الماضي" & vbCrLf & _
                        "3 - هذا العام" & vbCrLf & _
                        "4 - العام الماضي", "نطاق زمني سريع")
    
    Select Case DateRange
        Case "1"
            Me.txtFromDate = DateSerial(Year(Date), Month(Date), 1)
            Me.txtToDate = DateSerial(Year(Date), Month(Date) + 1, 0)
        Case "2"
            Me.txtFromDate = DateSerial(Year(Date), Month(Date) - 1, 1)
            Me.txtToDate = DateSerial(Year(Date), Month(Date), 0)
        Case "3"
            Me.txtFromDate = DateSerial(Year(Date), 1, 1)
            Me.txtToDate = DateSerial(Year(Date), 12, 31)
        Case "4"
            Me.txtFromDate = DateSerial(Year(Date) - 1, 1, 1)
            Me.txtToDate = DateSerial(Year(Date) - 1, 12, 31)
    End Select
End Sub

Private Sub Form_Load_LoadComboBoxes()
    ' تحميل قوائم المشاريع والحالات
    Me.cboProject.RowSource = "SELECT ProjectID, ProjectName FROM Projects ORDER BY ProjectName"
    Me.cboProject.Requery
    
    Me.cboStatus.RowSource = "'نشط';'مكتمل';'متوقف';'ملغي'"
    Me.cboStatus.Requery
End Sub

Private Sub lstReports_DblClick(Cancel As Integer)
    ' معاينة التقرير عند النقر المزدوج
    If Not IsNull(Me.lstReports) Then
        cmdPreview_Click
    End If
End Sub

Private Sub Form_Close()
    LogActivity "إغلاق نموذج", "Reports", 0, "إغلاق نموذج التقارير"
End Sub
