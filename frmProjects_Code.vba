' كود نموذج إدارة المشاريع
' Projects Management Form Code

Option Compare Database
Option Explicit

Private Sub Form_Load()
    ' تعيين خصائص النموذج
    Me.Caption = "إدارة المشاريع العقارية"
    Me.RightToLeft = True
    
    ' تعيين الصلاحيات
    SetFormPermissions
    
    ' تحديث قائمة المشاريع
    RefreshProjectsList
End Sub

Private Sub SetFormPermissions()
    ' تعيين الصلاحيات حسب نوع المستخدم
    Me.cmdAdd.Enabled = CheckPermission("المشاريع", "CanAdd")
    Me.cmdEdit.Enabled = CheckPermission("المشاريع", "CanEdit")
    Me.cmdDelete.Enabled = CheckPermission("المشاريع", "CanDelete")
    Me.cmdPrint.Enabled = CheckPermission("المشاريع", "CanPrint")
End Sub

Private Sub RefreshProjectsList()
    ' تحديث قائمة المشاريع
    Me.lstProjects.RowSource = "SELECT ProjectID, ProjectName, ProjectLocation, ProjectType, " & _
                               "ProjectStatus, CompletionPercentage, TotalCost " & _
                               "FROM Projects ORDER BY ProjectName"
    Me.lstProjects.Requery
End Sub

Private Sub cmdAdd_Click()
    ' إضافة مشروع جديد
    If Not CheckPermission("المشاريع", "CanAdd") Then
        MsgBox "ليس لديك صلاحية لإضافة مشروع جديد", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    ' فتح نموذج إضافة مشروع
    DoCmd.OpenForm "frmProjectDetails", acNormal, , , acFormAdd
End Sub

Private Sub cmdEdit_Click()
    ' تعديل المشروع المحدد
    If Not CheckPermission("المشاريع", "CanEdit") Then
        MsgBox "ليس لديك صلاحية لتعديل المشاريع", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstProjects) Then
        MsgBox "يرجى اختيار مشروع للتعديل", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' فتح نموذج تعديل المشروع
    DoCmd.OpenForm "frmProjectDetails", acNormal, , "ProjectID = " & Me.lstProjects, acFormEdit
End Sub

Private Sub cmdDelete_Click()
    ' حذف المشروع المحدد
    If Not CheckPermission("المشاريع", "CanDelete") Then
        MsgBox "ليس لديك صلاحية لحذف المشاريع", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstProjects) Then
        MsgBox "يرجى اختيار مشروع للحذف", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' التأكد من الحذف
    If MsgBox("هل أنت متأكد من حذف هذا المشروع؟" & vbCrLf & _
              "سيتم حذف جميع البيانات المرتبطة بالمشروع!", _
              vbYesNo + vbCritical, "تأكيد الحذف") = vbYes Then
        
        ' التحقق من وجود بيانات مرتبطة
        If HasRelatedData(Me.lstProjects) Then
            MsgBox "لا يمكن حذف هذا المشروع لوجود بيانات مرتبطة به", vbExclamation, "لا يمكن الحذف"
            Exit Sub
        End If
        
        ' حذف المشروع
        CurrentDb.Execute "DELETE FROM Projects WHERE ProjectID = " & Me.lstProjects
        
        ' تسجيل النشاط
        LogActivity "حذف", "Projects", Me.lstProjects, "حذف مشروع"
        
        ' تحديث القائمة
        RefreshProjectsList
        
        MsgBox "تم حذف المشروع بنجاح", vbInformation, "تم الحذف"
    End If
End Sub

Private Function HasRelatedData(ProjectID As Long) As Boolean
    ' التحقق من وجود بيانات مرتبطة بالمشروع
    HasRelatedData = False
    
    ' التحقق من الوحدات
    If DCount("*", "Units", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من عقود المقاولين
    If DCount("*", "ContractorContracts", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من الفواتير
    If DCount("*", "Invoices", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من طلبات الشراء
    If DCount("*", "PurchaseRequests", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من أعمال الصيانة
    If DCount("*", "Maintenance", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من التكاليف التشغيلية
    If DCount("*", "OperatingCosts", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
    
    ' التحقق من المهام
    If DCount("*", "Tasks", "ProjectID = " & ProjectID) > 0 Then
        HasRelatedData = True
        Exit Function
    End If
End Function

Private Sub cmdView_Click()
    ' عرض تفاصيل المشروع
    If IsNull(Me.lstProjects) Then
        MsgBox "يرجى اختيار مشروع للعرض", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' فتح نموذج عرض المشروع
    DoCmd.OpenForm "frmProjectDetails", acNormal, , "ProjectID = " & Me.lstProjects, acFormReadOnly
End Sub

Private Sub cmdUnits_Click()
    ' إدارة وحدات المشروع
    If IsNull(Me.lstProjects) Then
        MsgBox "يرجى اختيار مشروع أولاً", vbExclamation, "لم يتم الاختيار"
        Exit Sub
    End If
    
    ' فتح نموذج إدارة الوحدات
    DoCmd.OpenForm "frmUnits", acNormal, , "ProjectID = " & Me.lstProjects
End Sub

Private Sub cmdPrint_Click()
    ' طباعة تقرير المشاريع
    If Not CheckPermission("المشاريع", "CanPrint") Then
        MsgBox "ليس لديك صلاحية لطباعة التقارير", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    If IsNull(Me.lstProjects) Then
        ' طباعة جميع المشاريع
        DoCmd.OpenReport "rptProjects", acViewPreview
    Else
        ' طباعة المشروع المحدد
        DoCmd.OpenReport "rptProjectDetails", acViewPreview, , "ProjectID = " & Me.lstProjects
    End If
End Sub

Private Sub cmdSearch_Click()
    ' البحث في المشاريع
    Dim SearchText As String
    Dim SearchCriteria As String
    
    SearchText = InputBox("أدخل نص البحث:", "البحث في المشاريع")
    
    If Len(Trim(SearchText)) > 0 Then
        SearchCriteria = "ProjectName LIKE '*" & SearchText & "*' OR " & _
                        "ProjectLocation LIKE '*" & SearchText & "*' OR " & _
                        "Description LIKE '*" & SearchText & "*'"
        
        Me.lstProjects.RowSource = "SELECT ProjectID, ProjectName, ProjectLocation, ProjectType, " & _
                                   "ProjectStatus, CompletionPercentage, TotalCost " & _
                                   "FROM Projects WHERE " & SearchCriteria & " ORDER BY ProjectName"
        Me.lstProjects.Requery
        
        If Me.lstProjects.ListCount = 0 Then
            MsgBox "لم يتم العثور على نتائج", vbInformation, "البحث"
            RefreshProjectsList
        End If
    End If
End Sub

Private Sub cmdRefresh_Click()
    ' تحديث قائمة المشاريع
    RefreshProjectsList
    MsgBox "تم تحديث القائمة", vbInformation, "تحديث"
End Sub

Private Sub cmdFilter_Click()
    ' تصفية المشاريع
    Dim FilterForm As String
    FilterForm = "frmProjectFilter"
    
    ' فتح نموذج التصفية
    DoCmd.OpenForm FilterForm, acDialog
    
    ' تطبيق التصفية إذا تم اختيارها
    If Forms(FilterForm).Tag <> "" Then
        Me.lstProjects.RowSource = "SELECT ProjectID, ProjectName, ProjectLocation, ProjectType, " & _
                                   "ProjectStatus, CompletionPercentage, TotalCost " & _
                                   "FROM Projects WHERE " & Forms(FilterForm).Tag & " ORDER BY ProjectName"
        Me.lstProjects.Requery
    End If
    
    DoCmd.Close acForm, FilterForm
End Sub

Private Sub cmdExport_Click()
    ' تصدير البيانات إلى Excel
    If Not CheckPermission("المشاريع", "CanPrint") Then
        MsgBox "ليس لديك صلاحية لتصدير البيانات", vbExclamation, "صلاحيات غير كافية"
        Exit Sub
    End If
    
    On Error GoTo ErrorHandler
    
    Dim ExportPath As String
    ExportPath = CurrentProject.Path & "\Projects_Export_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".xlsx"
    
    ' تصدير البيانات
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, "Projects", ExportPath, True
    
    MsgBox "تم تصدير البيانات بنجاح إلى:" & vbCrLf & ExportPath, vbInformation, "تصدير البيانات"
    
    ' تسجيل النشاط
    LogActivity "تصدير", "Projects", 0, "تصدير بيانات المشاريع"
    
    Exit Sub
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تصدير البيانات: " & Err.Description, vbCritical, "خطأ"
End Sub

Private Sub lstProjects_DblClick(Cancel As Integer)
    ' فتح تفاصيل المشروع عند النقر المزدوج
    If Not IsNull(Me.lstProjects) Then
        cmdView_Click
    End If
End Sub

Private Sub Form_Close()
    ' تسجيل النشاط عند إغلاق النموذج
    LogActivity "إغلاق نموذج", "Projects", 0, "إغلاق نموذج إدارة المشاريع"
End Sub
