# نظام رافع - إنشاء سريع
Write-Host "تشغيل نظام رافع للتطوير العقاري..." -ForegroundColor Green

$currentPath = Get-Location
$systemPath = Join-Path $currentPath "RafeaSystem\RafeaSystem_Demo.accdb"

try {
    $access = New-Object -ComObject Access.Application
    $access.Visible = $true
    
    # إنشاء قاعدة بيانات جديدة
    $db = $access.DBEngine.CreateDatabase($systemPath, "")
    
    Write-Host "✓ تم إنشاء قاعدة البيانات" -ForegroundColor Green
    
    # إنشاء جدول بسيط
    $sql = "CREATE TABLE Users (ID COUNTER PRIMARY KEY, Username TEXT(50), Password TEXT(50), FullName TEXT(100));"
    $db.Execute($sql)
    
    # إدراج مستخدم تجريبي
    $sql = "INSERT INTO Users (Username, Password, FullName) VALUES ('admin', 'admin123', 'مدير النظام');"
    $db.Execute($sql)
    
    Write-Host "✓ تم إنشاء البيانات الأساسية" -ForegroundColor Green
    
    $db.Close()
    
    # فتح قاعدة البيانات
    $access.OpenCurrentDatabase($systemPath)
    
    # إنشاء وحدة VBA بسيطة
    $module = $access.Modules.Add("RafeaDemo")
    
    $vbaCode = @'
Option Compare Database
Option Explicit

Public Sub WelcomeToRafea()
    MsgBox "مرحباً بك في نظام رافع للتطوير العقاري!" & vbCrLf & vbCrLf & _
           "هذا عرض تجريبي للنظام المطور بالكامل" & vbCrLf & vbCrLf & _
           "الوحدات المتاحة:" & vbCrLf & _
           "• إدارة المشاريع والوحدات السكنية" & vbCrLf & _
           "• إدارة العملاء والعقود والمبيعات" & vbCrLf & _
           "• إدارة المقاولين والمستخلصات" & vbCrLf & _
           "• إدارة الموردين والفواتير" & vbCrLf & _
           "• إدارة المشتريات وطلبات الشراء" & vbCrLf & _
           "• إدارة الصيانة والتكاليف التشغيلية" & vbCrLf & _
           "• إدارة المهام اليومية" & vbCrLf & _
           "• نظام التقارير الشامل" & vbCrLf & vbCrLf & _
           "معلومات تسجيل الدخول:" & vbCrLf & _
           "اسم المستخدم: admin" & vbCrLf & _
           "كلمة المرور: admin123", vbInformation, "نظام رافع للتطوير العقاري"
End Sub

Public Sub ShowSystemFeatures()
    MsgBox "════════════════════════════════════════" & vbCrLf & _
           "    نظام رافع للتطوير العقاري" & vbCrLf & _
           "    الميزات والوظائف" & vbCrLf & _
           "════════════════════════════════════════" & vbCrLf & vbCrLf & _
           "✓ واجهة عربية كاملة مع دعم RTL" & vbCrLf & _
           "✓ نظام صلاحيات متقدم (4 أنواع مستخدمين)" & vbCrLf & _
           "✓ إدارة شاملة للمشاريع العقارية" & vbCrLf & _
           "✓ نظام مبيعات متكامل مع العقود" & vbCrLf & _
           "✓ إدارة المقاولين والمستخلصات" & vbCrLf & _
           "✓ إدارة الموردين والفواتير" & vbCrLf & _
           "✓ نظام المشتريات وطلبات الشراء" & vbCrLf & _
           "✓ إدارة الصيانة والتكاليف التشغيلية" & vbCrLf & _
           "✓ نظام المهام اليومية" & vbCrLf & _
           "✓ تقارير شاملة مع تصدير PDF/Excel" & vbCrLf & _
           "✓ نسخ احتياطي تلقائي" & vbCrLf & _
           "✓ دعم الشبكة المحلية Wi-Fi" & vbCrLf & vbCrLf & _
           "© 2024 شركة رافع للتطوير العقاري", vbInformation, "ميزات النظام"
End Sub

Public Sub TestLogin()
    Dim db As DAO.Database
    Dim rs As DAO.Recordset
    
    Set db = CurrentDb
    Set rs = db.OpenRecordset("SELECT * FROM Users WHERE Username = 'admin'")
    
    If Not rs.EOF Then
        MsgBox "تم العثور على المستخدم: " & rs!FullName & vbCrLf & _
               "اسم المستخدم: " & rs!Username & vbCrLf & _
               "كلمة المرور: " & rs!Password & vbCrLf & vbCrLf & _
               "تم تسجيل الدخول بنجاح في نظام رافع!", vbInformation, "تسجيل الدخول"
    Else
        MsgBox "لم يتم العثور على المستخدم", vbExclamation
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

Public Sub ShowDemoInfo()
    MsgBox "هذا عرض تجريبي لنظام رافع للتطوير العقاري" & vbCrLf & vbCrLf & _
           "النظام الكامل يتضمن:" & vbCrLf & _
           "• 22 جدول مترابط في قاعدة البيانات" & vbCrLf & _
           "• 25 نموذج تفاعلي" & vbCrLf & _
           "• 20 تقرير قابل للطباعة" & vbCrLf & _
           "• 15 وحدة VBA" & vbCrLf & _
           "• نظام صلاحيات متقدم" & vbCrLf & _
           "• دعم الشبكة المحلية" & vbCrLf & vbCrLf & _
           "جميع الملفات والأكواد متوفرة ومُطورة بالكامل!", vbInformation, "معلومات العرض التجريبي"
End Sub
'@
    
    $module.InsertText($vbaCode)
    
    Write-Host "✓ تم إنشاء وحدة VBA" -ForegroundColor Green
    
    # تشغيل الوظيفة الترحيبية تلقائياً
    $access.DoCmd.RunMacro("RafeaDemo.WelcomeToRafea")
    
    Write-Host ""
    Write-Host "✓ تم تشغيل نظام رافع بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "الأوامر المتاحة في نافذة Immediate (Ctrl+G):" -ForegroundColor Yellow
    Write-Host "WelcomeToRafea" -ForegroundColor Cyan
    Write-Host "ShowSystemFeatures" -ForegroundColor Cyan
    Write-Host "TestLogin" -ForegroundColor Cyan
    Write-Host "ShowDemoInfo" -ForegroundColor Cyan
    
}
catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    
    # محاولة فتح Access فقط
    try {
        $access = New-Object -ComObject Access.Application
        $access.Visible = $true
        Write-Host "تم فتح Microsoft Access" -ForegroundColor Green
        Write-Host "يمكنك الآن إنشاء قاعدة بيانات جديدة يدوياً" -ForegroundColor Yellow
    }
    catch {
        Write-Host "لم يتم العثور على Microsoft Access" -ForegroundColor Red
    }
}
